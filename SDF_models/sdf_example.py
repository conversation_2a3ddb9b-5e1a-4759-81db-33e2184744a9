import os

os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
import numpy as np
import trimesh
import mcubes
import torch
from sdf import *


def get_rotation_mat_x(theta):
    return np.array([[1, 0, 0], [0, np.cos(theta), -np.sin(theta)],
                     [0, np.sin(theta), np.cos(theta)]])


def get_rotation_mat_y(theta):
    return np.array([[np.cos(theta), 0, np.sin(theta)], [0, 1, 0],
                     [-np.sin(theta), 0, np.cos(theta)]])


def get_rotation_mat_z(theta):
    return np.array([[np.cos(theta), -np.sin(theta), 0],
                     [np.sin(theta), np.cos(theta), 0], [0, 0, 1]])


def process(bd_min, bd_max, save_ply_path):
    Rx = get_rotation_mat_x(-90.0 / 180.0 * np.pi)
    resolution = 128

    N = 8
    assert resolution % N == 0
    X = np.linspace(bd_min[0], bd_max[0], resolution)
    X = np.array_split(X, N)
    Y = np.linspace(bd_min[1], bd_max[1], resolution)
    Y = np.array_split(Y, N)
    Z = np.linspace(bd_min[2], bd_max[2], resolution)
    Z = np.array_split(Z, N)

    v = np.zeros([resolution, resolution, resolution], dtype=np.float32)

    for xi, xs in enumerate(X):
        for yi, ys in enumerate(Y):
            for zi, zs in enumerate(Z):
                xx, yy, zz = np.meshgrid(xs, ys, zs, indexing='ij')
                # xx, yy, zz = np.meshgrid(xs, ys, zs)
                pts = np.concatenate(
                    [xx.reshape(-1, 1),
                     yy.reshape(-1, 1),
                     zz.reshape(-1, 1)],
                    axis=-1)
                val = sdf_func(pts).reshape(len(xs), len(ys), len(zs))
                v[xi * int(resolution / N):xi * int(resolution / N) + len(xs),
                  yi * int(resolution / N):yi * int(resolution / N) + len(ys),
                  zi * int(resolution / N):zi * int(resolution / N) +
                  len(zs)] = val

    vertices, triangles = mcubes.marching_cubes(v, 0)
    b_max_np = bd_max
    b_min_np = bd_min

    vertices = vertices / (resolution - 1.0) * (
        b_max_np - b_min_np)[None, :] + b_min_np[None, :]
    mesh = trimesh.Trimesh(vertices, triangles)

    # 保存结果为pt文件
    # save_res = {}
    # save_res['min_bound'] = bd_min
    # save_res['max_bound'] = bd_max
    # save_res['grid_tensor'] = v.reshape(
    #     (1, 1, resolution, resolution, resolution)).astype(np.float32)
    # torch.save(save_res, 'SDF_models/tmp/box.pt')

    mesh.export(save_ply_path)


if __name__ == "__main__":
    # sdf_func = (rounded_cylinder(0.5, 0.1, 2).translate((-0.5, 0, 0))
    #             | rounded_cylinder(0.5, 0.1, 2).translate(
    #                 (0.5, 0, 0))).rotate(np.pi / 4, Y)
    # sdf_func = rounded_cone(0.1, 0.05, 2.2).translate((0, 0, -1.1))
    # sdf_func = elongate(box(0.1), (0.25, 0.5, 0.75))
    sdf_func = wireframe_box((1, 2, 3), 0.2)

    bd_min = np.array([-2, -2, -2]) - 0.05
    bd_max = np.array([2, 2, 2]) + 0.05
    ply_path = 'SDF_models/tmp/elongate.ply'
    process(bd_min, bd_max, ply_path)
