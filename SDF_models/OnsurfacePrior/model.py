import numpy as np
#import tensorflow as tf
import tensorflow.compat.v1 as tf
import mmcv
from glob import glob
from tqdm import tqdm

tf.disable_v2_behavior()
import os

POINT_NUM_SPARSE = 500
POINT_NUM = 4096
test_num = 2000
feature_object = tf.placeholder(tf.float32, shape=[POINT_NUM, test_num])
input_points_3d = tf.placeholder(tf.float32, shape=[POINT_NUM, 3])


def safe_norm(x, epsilon=1e-12, axis=None):
    return tf.sqrt(tf.reduce_sum(x**2, axis=axis) + epsilon)


def g_decoder(feature_g, input_points_3d_g):
    with tf.variable_scope('global', reuse=tf.AUTO_REUSE):

        feature_f = tf.nn.relu(tf.layers.dense(feature_g, 128))
        net = tf.nn.relu(tf.layers.dense(input_points_3d_g, 512))
        net = tf.concat([net, feature_f], 1)
        print('net:', net)
        with tf.variable_scope('decoder', reuse=tf.AUTO_REUSE):
            for i in range(8):
                with tf.variable_scope("resnetBlockFC_%d" % i):
                    b_initializer = tf.constant_initializer(0.0)
                    w_initializer = tf.random_normal_initializer(
                        mean=0.0, stddev=np.sqrt(2) / np.sqrt(512))
                    net = tf.layers.dense(tf.nn.relu(net),
                                          512,
                                          kernel_initializer=w_initializer,
                                          bias_initializer=b_initializer)

        b_initializer = tf.constant_initializer(-0.5)
        w_initializer = tf.random_normal_initializer(mean=2 * np.sqrt(np.pi) /
                                                     np.sqrt(512),
                                                     stddev=0.000001)
        print('net:', net)
        sdf = tf.layers.dense(tf.nn.relu(net),
                              1,
                              kernel_initializer=w_initializer,
                              bias_initializer=b_initializer)
        grad = tf.gradients(ys=sdf, xs=input_points_3d_g)
        print('grad', grad)
        print(grad[0])
        normal_p_lenght = tf.expand_dims(safe_norm(grad[0], axis=-1), -1)
        print('normal_p_lenght', normal_p_lenght)
        grad_norm = grad[0] / (normal_p_lenght + 1e-12)
        print('grad_norm', grad_norm)

        g_points = input_points_3d_g - sdf * grad_norm
        return sdf, g_points


sdf, g_points = g_decoder(feature_object, input_points_3d)

config = tf.ConfigProto(allow_soft_placement=False)
saver = tf.train.Saver(max_to_keep=2000000)

ckpt_path = ''
with tf.Session(config=config) as sess:
    feature_bs = []

    for i in range(test_num):
        t = np.zeros(test_num)
        t[i] = 1
        feature_bs.append(t)

    feature_bs = np.asarray(feature_bs)
    saver.restore(sess, ckpt_path)
