import numpy as np
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from scene._utils import create_look_at_matrix, calculate_stable_rotation


# --- 验证 ---
# 场景设置
scene_center = np.array([4, 0, 20])
pos1 = np.array([0, 0, 0])
pos2 = np.array([4, -2, 20])
world_up = np.array([0, 1, 0])

# 计算初始和最终姿态
R1 = create_look_at_matrix(pos1, scene_center, world_up)
R2 = create_look_at_matrix(pos2, scene_center, world_up)

print("初始姿态矩阵 R1 (雷达在pos1的坐标系):\n", np.round(R1, 2))
print("\n最终姿态矩阵 R2 (雷达在pos2的坐标系):\n", np.round(R2, 2))

# 计算从姿态1到姿态2的旋转
R_transform = calculate_stable_rotation(pos1, pos2, scene_center, world_up)

print("\n计算出的变换旋转矩阵 R_transform:\n", np.round(R_transform, 2))

# 验证：将变换矩阵应用到初始姿态上，看是否等于最终姿态
R1_transformed = R_transform @ R1
print("\n验证: R_transform @ R1:\n", np.round(R1_transformed, 2))

assert np.allclose(R1_transformed, R2), "验证失败！变换矩阵不正确。"
print("\n验证成功！R_transform @ R1 == R2")

z_axis = scene_center-pos1
z_axis = z_axis/np.linalg.norm(z_axis)
print(R_transform@z_axis)
