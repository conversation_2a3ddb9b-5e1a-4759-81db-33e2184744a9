import numpy as np
import torch
from tqdm import tqdm
from copy import deepcopy
import time
import sys
import os
from glob import glob
from scipy.spatial import cKDTree
import cv2
import json
import random
import multiprocessing
from typing import DefaultDict
from scene.scene import ObjectFactory
import sdf

current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(os.path.dirname(current_file_path))
sys.path.append(current_dir)

from scripts.pcd_material import PCDMaterial
from scripts import util
from scene import Scene, GridObject, Lidar, _utils, cls2label,label2cls, \
    insert_person_objs, DiaoBi, get_link_point, insert_bg_objs, ShengZi, insert_qianyinsheng_objs,\
    insert_diaowu,insert_diaoBi

np.random.seed()


def collect_shapenet_grids():
    folder = '/data/zhaoxin_data/dataset/3D/shapenetcore_partanno_segmentation_benchmark_v0_normal/onSurprior_exp/ckpts_04'
    paths = glob(os.path.join(folder, '*/*pt'))
    return paths


def collect_person_grids():
    folder = 'SDF_models/grid_pts/person'
    paths = glob(os.path.join(folder, '*pt'))
    return paths


def collect_diaobi_grids():
    # return glob('SDF_models/grid_pts/diaobi/*pt')
    return ['SDF_models/grid_pts/diaobi/diaobi_long.pt']


def test_get_link_pt():
    diaowu_pos = np.array([0, -5, 60])
    lidar_pos = np.array([0, -15, 60])
    diaowu_obj = GridObject(
        grid_sdf_path=
        '/data/zhaoxin_data/dataset/3D/shapenetcore_partanno_segmentation_benchmark_v0_normal/onSurprior_exp/ckpts_04/31a96fc694f0e58cd5dde04c96fd8146_1000_0.25/data.pt',
        t=diaowu_pos,
        angles=[np.pi / 4, 0, 0],
        obj_scale=5,
        label=cls2label['diaoWu'])
    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi,
                  -4664 / 60 * 2 * np.pi)

    R = lidar.get_lidar_R(target_d=diaowu_pos - lidar_pos)
    lidar.moveto(lidar_pos)
    lidar.rotate(R_matrix=R)

    link_pts = get_link_point(lidar, 4, diaowu_obj)[:, :3]
    line_obj_list = []
    diaobi_pos = np.array([0, -10, 60])
    for pt in link_pts:
        # pts = util.rendering_line(lidar_pos, pt)
        rope_scale, rope_R, rope_t = _utils.get_cylinder_params(
            'SDF_models/grid_pts/cylinder/cylinder.pt', diaobi_pos, pt)
        line_obj = ShengZi(
            grid_sdf_path='SDF_models/grid_pts/cylinder/cylinder.pt',
            t=rope_t,
            angles=rope_R,
            # angles=[0, 0, 0],
            obj_scale=rope_scale,
            # obj_scale=3,
            label=cls2label['qianYinSheng'])
        line_obj_list.append(line_obj)
    scene_obj = Scene([diaowu_obj] + line_obj_list)
    new_lidar_pos = np.array([0, 0, 0])
    R = lidar.get_lidar_R(ori_d=diaowu_pos - lidar_pos,
                          target_d=np.array([0, 0, 60]) - new_lidar_pos)
    lidar.moveto(new_lidar_pos)
    lidar.rotate(R_matrix=R)
    scene_pts = lidar.synthethis(scene_obj,
                                 0,
                                 1,
                                 50000,
                                 0.01,
                                 return_scene_coord=True)
    print(len(scene_pts))
    np.savetxt('results/link_pt.txt', scene_pts)


def test_collision():
    obj1 = PCDMaterial(
        grid_sdf_path=
        '/data/zhaoxin_data/dataset/3D/shapenetcore_partanno_segmentation_benchmark_v0_normal/onSurprior_exp/ckpts_04/f378404d31ce9db1afe1d4530f4c6e24_1000_0.25/data.pt',
        obj_center=[10, -2, 10],
        # obj_angles=[0, 0, np.pi],
        obj_angles=[0, 0, 0],
        obj_scale=6,
        label=cls2label['bg'],
        update_center=False)
    obj2 = PCDMaterial(
        grid_sdf_path=
        '/data/zhaoxin_data/dataset/3D/shapenetcore_partanno_segmentation_benchmark_v0_normal/onSurprior_exp/ckpts_04/f378404d31ce9db1afe1d4530f4c6e24_1000_0.25/data.pt',
        obj_center=[10, -2, 8],
        # obj_angles=[0, 0, np.pi],
        obj_angles=[0, 0, 0],
        obj_scale=6,
        label=cls2label['bg'],
        update_center=False)
    obj1 = GridObject(
        torch.permute(
            obj1.grid_sdf_matrix.reshape(*obj1.grid_sdf_matrix.shape[-3:]),
            [2, 1, 0]), obj1.obj_size, obj1.obj_center, obj1.obj_angles,
        obj1.label)
    obj2 = GridObject(
        torch.permute(
            obj2.grid_sdf_matrix.reshape(*obj2.grid_sdf_matrix.shape[-3:]),
            [2, 1, 0]), obj2.obj_size, obj2.obj_center, obj2.obj_angles,
        obj2.label)
    dist = Scene.cal_obj_min_distance(obj1, obj2)
    print(dist)


def test_rendering_scene(args):
    scene_idx, save_folder = args
    np.random.seed(scene_idx)
    save_lidar_pcd_folder = os.path.join(save_folder, 'lidar')
    save_scene_pcd_folder = os.path.join(save_folder, 'scene')
    save_scene_info_folder = os.path.join(save_folder, 'json')
    _utils.mkdir_or_exist(save_lidar_pcd_folder)
    _utils.mkdir_or_exist(save_scene_pcd_folder)
    _utils.mkdir_or_exist(save_scene_info_folder)
    scene_info = {}
    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi,
                  -4664 / 60 * 2 * np.pi)
    scene_center = np.array([0.6, -1.8, 30])  # we will update it later
    scene_center[2] += np.random.uniform(-10, 10)
    scene_obj = Scene([])

    shapenet_grid_paths = collect_shapenet_grids()
    person_grid_paths = collect_person_grids()
    diaobi_grid_paths = collect_diaobi_grids()
    np.random.shuffle(shapenet_grid_paths)

    diaobi_obj = insert_diaoBi(scene_center=scene_center,
                               diaobi_grid_paths=diaobi_grid_paths)

    # update scene center
    diaobi_upper_coord = diaobi_obj.update_diaobi_upper_pt()
    scene_center = deepcopy(diaobi_upper_coord)
    scene_center[0] += np.random.uniform(-1, 1)
    scene_center[1] = np.random.uniform(-1, -6)
    print("scene_center: ", scene_center)
    scene_info['scene_center'] = scene_center.tolist()
    scene_info['diaobi_upper_coord'] = diaobi_upper_coord.tolist()

    lidar.move(LIDAR_ORI_POS, scene_center)
    scan_radius = util.cal_scan_radius_by_fov(fov=70.4,
                                              viewpoint=LIDAR_ORI_POS,
                                              plane_center=scene_center)
    scene_info['scan_r'] = scan_radius

    # insert people
    person_objs = insert_person_objs(diaobi_obj=diaobi_obj,
                                     scene_center=scene_center,
                                     scan_radius=scan_radius,
                                     person_grid_paths=person_grid_paths)
    print(f"rendering {len(person_objs)} person")
    if len(person_objs) == 0: return

    diaowu_obj_list, diaowu_up_link_pts = insert_diaowu(
        diaobi_upper_coord, shapenet_grid_paths, diaobi_obj, scene_center,
        lidar)
    print('diaowu is ok')
    if len(diaowu_up_link_pts) == 0:
        print('can not find enough diaowu link pts')
        return
    diaowu_pos = np.mean(np.concatenate(
        [obj.obj2scene[:, 3].reshape(1, 3) for obj in diaowu_obj_list], 0),
                         axis=0)

    # insert diaosheng
    diaosheng_obj_list = []
    # 添加吊臂顶点到分叉吊绳之间的吊绳
    bifurcation_point = np.random.uniform(
        0.5, 0.8) * (diaowu_pos - diaobi_upper_coord) + diaobi_upper_coord
    scene_info['bifurcation_point'] = bifurcation_point.tolist()
    # bifurcation_point[0] += 0.01
    rope_scale, rope_R, rope_t = _utils.get_cylinder_params(
        'SDF_models/grid_pts/cylinder/cylinder.pt', bifurcation_point,
        diaobi_upper_coord)
    diaosheng_obj = ShengZi(
        grid_sdf_path='SDF_models/grid_pts/cylinder/cylinder.pt',
        t=rope_t,
        angles=rope_R,
        obj_scale=rope_scale,
        label=cls2label['diaoSheng'])
    diaosheng_obj_list.append(diaosheng_obj)

    for pt in diaowu_up_link_pts:
        rope_scale, rope_R, rope_t = _utils.get_cylinder_params(
            'SDF_models/grid_pts/cylinder/cylinder.pt', bifurcation_point, pt)
        diaosheng_obj = ShengZi(
            grid_sdf_path='SDF_models/grid_pts/cylinder/cylinder.pt',
            t=rope_t,
            angles=rope_R,
            obj_scale=rope_scale,
            label=cls2label['diaoSheng'])
        diaosheng_obj_list.append(diaosheng_obj)

    qianyinsheng_obj_list = insert_qianyinsheng_objs(
        lidar=lidar,
        shengzi_grid_pt_path='SDF_models/grid_pts/cylinder/cylinder.pt',
        diaowu_obj_list=diaowu_obj_list,
        person_obj_list=person_objs)
    print(f'rendering {len(qianyinsheng_obj_list)} qianyinsheng')
    scene_obj_list = [diaobi_obj
                      ] + person_objs + qianyinsheng_obj_list + diaowu_obj_list

    # insert background
    bg_around_diaowu = []
    bg_around_diaobi = []
    bg_obj_list = insert_bg_objs(scene_obj_list=scene_obj_list,
                                 bg_grid_paths=shapenet_grid_paths,
                                 scene_center=scene_center,
                                 r=13,
                                 num_bg=np.random.randint(15, 40),
                                 max_size=5)
    bg_around_diaowu = insert_bg_objs(scene_obj_list=scene_obj_list,
                                      bg_grid_paths=shapenet_grid_paths,
                                      scene_center=diaowu_pos,
                                      r=5,
                                      num_bg=np.random.randint(3, 6),
                                      max_size=4)
    diaobi_pts = diaobi_obj.sample_neg_grid_pts()
    index = np.argsort(diaobi_pts[:, 1])[-1]
    diaobi_base_pos = diaobi_pts[index]  # 吊臂底座坐标

    bg_around_diaobi = insert_bg_objs(scene_obj_list=scene_obj_list,
                                      bg_grid_paths=shapenet_grid_paths,
                                      scene_center=diaobi_base_pos,
                                      r=5,
                                      num_bg=np.random.randint(3, 6),
                                      max_size=3)

    bg_obj_list = bg_around_diaobi + bg_around_diaowu + bg_obj_list
    # bg_obj_list = []
    print(f'rendering {len(bg_obj_list)} bg')
    scene_obj_list = scene_obj_list + bg_obj_list + diaosheng_obj_list
    scene_obj.models = scene_obj_list

    lidar_pos = np.array([0, np.random.uniform(-5, -1), 0])
    lidar_pos_list = _utils.sample_lidar_pos(scene_center=scene_center,
                                             lidar_ori=lidar_pos,
                                             num_lidar=6)
    obj_ori_pos_list = [model.obj2scene[:, 3] for model in scene_obj.models]
    for pos_idx, new_lidar_pos in enumerate(lidar_pos_list):
        # 物体归位,以防乱走后互相重叠了
        for model, pos in zip(scene_obj.models, obj_ori_pos_list):
            model.moveto(pos)
        lidar.move(new_lidar_pos, scene_center)
        start = 0
        scene_pts = []
        debug_scene_pts = []
        delta = np.random.randint(1, 5, (3, ))
        delta[1] = 0

        # ghost
        for _ in range(10):
            pts = lidar.synthethis(scene_obj,
                                start,
                                0.1,
                                7500,
                                0.001,
                                return_scene_coord=False)

            if len(pts) > 0:
                scene_pts.append(pts)
                debug_scene_pts.append(lidar.convert2scene(pts))
            for obj_idx, model in enumerate(scene_obj.models):
                current_loc = model.obj2scene[:, 3]
                # if model.label in [
                #         cls2label['qianYinSheng'], cls2label['diaoSheng']
                # ]:
                #     model.moveto(current_loc + delta * 0.004)
                # else:
                model.moveto(current_loc + delta * 0.01)
            start += 0.1
        if len(scene_pts) == 0: continue
        scene_pts = np.concatenate(scene_pts, 0)
        debug_scene_pts = np.concatenate(debug_scene_pts, 0)
        print(f'rendering {len(scene_pts)} pts')
        if len(scene_pts) > 0:
            save_txt_path = os.path.join(
                save_lidar_pcd_folder, f'scene{scene_idx}_lidar{pos_idx}.txt')
            save_debugtxt_path = os.path.join(
                save_scene_pcd_folder,
                f'scene{scene_idx}_lidar{pos_idx}_scenepts.txt')
            print(save_txt_path)
            np.savetxt(save_txt_path, scene_pts[:, :4])
            np.savetxt(save_debugtxt_path, debug_scene_pts[:, :4])
            scene_info_path = os.path.join(
                save_scene_info_folder,
                f'scene{scene_idx}_lidar{pos_idx}.json')
            record_rendering_info(lidar=lidar,
                                models=scene_obj.models,
                                scene_infos=scene_info,
                                save_json_path=scene_info_path)


def record_rendering_info(lidar, models, scene_infos, save_json_path):
    info = {
        "lidar_pos": lidar.lidar2scene[:, 3].tolist(),
        "lidar_R": lidar.lidar2scene[:, :3].tolist(),
    }
    info.update(scene_infos)
    for i, model in enumerate(models):
        key = f"obj{model.ID}_{label2cls[model.label]}"
        info[key] = {
            "grid_path": model.grid_path,
            "size": model.size.tolist(),
            "label": model.label,
            "R": model.obj2scene[:, :3].tolist(),
            "t": model.obj2scene[:, 3].tolist()
        }
    jsonstr = json.dumps(info)
    with open(save_json_path, 'w') as f:
        f.write(jsonstr)


def test_cylinder():
    # scene_center = np.array([0, -15, 40])

    # p1 p2 满足： p1_y < p2_y
    p1 = np.array([3, 2, 8])
    p2 = np.array([2, 0, 4])
    rope_scale, rope_R, rope_t = _utils.get_cylinder_params(
        r'D:\python_projects\Crane_LiDAR_simulation_old\cylinder.pt', p1, p2)
    print(rope_scale)
    print(rope_R)
    np.savetxt('p1.txt', p1[None, :])
    np.savetxt('p2.txt', p2[None, :])

    # diaobi_pos = scene_center
    # diaobi_obj = GridObject(
    diaobi_obj = ShengZi(
        grid_sdf_path=
        # '/data/zhaoxin_data/dataset/3D/lidar_eye/diaobi/ckpts/001_fps_diaobi_camera_coor_1000_1.0/data.pt',
        r'D:\python_projects\Crane_LiDAR_simulation_old\cylinder.pt',
        t=rope_t,
        angles=rope_R,
        # angles=[0, 0, 0],
        obj_scale=rope_scale,
        # obj_scale=3,
        label=cls2label['qianYinSheng'])

    # scene_obj_list = [diaobi_obj, diaowu_obj] + person_objs
    scene_obj_list = [diaobi_obj]

    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi,
                  -4664 / 60 * 2 * np.pi)
    # lidar_pos = deepcopy(diaobi_obj.obj_center.numpy())
    # lidar_pos[1] = 50
    # lidar.moveto([0, 2, 0])
    # lidar_R = lidar.get_lidar_R(np.array([0, 0, 1]), diaobi_pos - lidar_pos)
    # lidar.rotate(R_matrix=lidar_R)
    scene = Scene(scene_obj_list)
    scene_pts = lidar.synthethis(scene,
                                 0,
                                 1,
                                 50000,
                                 0.001,
                                 return_scene_coord=False)
    # scene_pts = lidar.move_obj_to_ground(scene_pts, label_list=[2, 3])
    print(f'rendering {len(scene_pts)} pts')
    # 获取姿态变化后的吊臂顶点在雷达坐标系中的坐标
    # diaobi_pt_lidar = lidar.get_diaobi_upper_pt_lidar(scene)
    # print(diaobi_pt_lidar)
    if len(scene_pts) > 0:
        # np.savetxt('results/scene.txt', scene_pts[:, :4])
        # save_pts = np.concatenate((scene_pts[:, :4], p1[None, :], p2[None, :]), axis=0)
        np.savetxt('scene.txt', scene_pts[:, :4])
        # np.savetxt('scene.txt', save_pts)


def test_lidar_rotate():
    scene_center = np.array([0, 0, 10])
    diaowu_obj = GridObject(
        grid_sdf_path=
        '/data/zhaoxin_data/dataset/3D/shapenetcore_partanno_segmentation_benchmark_v0_normal/onSurprior_exp/ckpts_04/31a96fc694f0e58cd5dde04c96fd8146_1000_0.25/data.pt',
        t=scene_center,
        angles=[0, 0, 0],
        obj_scale=5,
        label=cls2label['diaoWu'])
    scene_obj_list = [diaowu_obj]
    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi,
                  -4664 / 60 * 2 * np.pi)
    p_lidar_new = np.array([10, 0, 10])
    
    R = _utils.get_rotation_mat('y', [np.pi / 2])
    lidar.rotate(R_matrix=R)
    lidar.moveto(p_lidar_new)
    scene_obj = Scene(scene_obj_list)
    scene_pts = lidar.synthethis(scene_obj,
                                 0,
                                 1,
                                 40000,
                                 0.01,
                                 return_scene_coord=True)
    print(f'rendering {len(scene_pts)} pts')
    np.savetxt('results/tmp/lidar1.txt', scene_pts[:, :4])

    # p_lidar_ori = np.array([0, 0, 0])
    # p_lidar_new = np.array([10, 0, 10])
    # R, t = _utils.get_lidar_pos_params(p_obj=scene_center,
    #                                    p_lidar_ori=p_lidar_ori,
    #                                    p_lidar_new=p_lidar_new)

    # lidar.moveto(t)
    # lidar.rotate(R_matrix=R)

    # scene_pts = lidar.synthethis(scene_obj,
    #                              0,
    #                              1,
    #                              40000,
    #                              0.01,
    #                              return_scene_coord=True)
    # print(f'rendering {len(scene_pts)} pts')
    # np.savetxt('results/tmp/lidar2.txt', scene_pts[:, :4])



def test_move_camera():
    scene_center = np.array([2, 0, 20])
    lidar_ori_pos = np.array([0, 0, 0])

    # diaowu_obj = ObjectFactory.get_rounded_box(t=scene_center.tolist(),
    #                                            angles=[0, 0, 0],
    #                                            xyz_size=[4, 4, 4])
    diaowu_obj = ObjectFactory.get_sphere(t=scene_center, angles=[0,0,0],r=2)

    scene_obj_list = [diaowu_obj]
    scene_obj = Scene(scene_obj_list)
    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi,
                  -4664 / 60 * 2 * np.pi)


    # R_trans = _utils.init_lidar_pose(lidar_ori_pos,scene_center)
    # R_trans, _ = _utils.get_lidar_pos_params([0,0,0],[0,0,1],lidar_ori_pos,scene_center)
    # lidar.moveto(lidar_ori_pos)
    # lidar.rotate(R_matrix=R_trans)
    lidar.move(lidar_ori_pos, scene_center)

    
    scene_pts = []

    lidar_pos1 = np.array([1, -10, 20])
    lidar_pos2 = np.array([2, 5, 20])
    lidar_pos_list = [lidar_pos1, lidar_pos2]
    for lidar_pos in lidar_pos_list:
        lidar.move(lidar_pos, scene_center)

        pts = lidar.synthethis(scene_obj,
                                0,
                                1,
                                100000,
                                0.01,
                                return_scene_coord=True)
        print('render {} pts'.format(len(pts)))
        if len(pts) > 0:
                scene_pts.append(pts)

    scene_pts = np.concatenate(scene_pts, 0)
    print(f'rendering {len(scene_pts)} pts')
    np.savetxt('results/diaowu_obj_0.txt', scene_pts[:, :4])

def test_render_sdf():
    scene_center = np.array([2, 0, 20])
    lidar_ori_pos = np.array([0, 0, 0])

    # diaowu_obj = ObjectFactory.get_rounded_box(t=scene_center.tolist(),
    #                                            angles=[0, 0, 0],
    #                                            xyz_size=[4, 4, 4])
    # diaowu_obj = ObjectFactory.get_sphere(t=scene_center, angles=[0,0,0],r=2)
    diaowu_obj = ObjectFactory.get_random_obj(t=scene_center,min_size=3,max_size=10,label=1)

    scene_obj_list = [diaowu_obj]
    scene_obj = Scene(scene_obj_list)
    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi,
                  -4664 / 60 * 2 * np.pi)

    lidar_pos_list = _utils.sample_lidar_pos(scene_center, lidar_ori_pos, num_lidar=4)
    result = []
    for lidar_pos in lidar_pos_list:
        lidar.move(lidar_pos, scene_center)

        scene_pts = lidar.synthethis(scene_obj,
                                    0,
                                    1,
                                    100000,
                                    0.01,
                                    return_scene_coord=True)
        print(f'rendering {len(scene_pts)} pts')
        result.extend(scene_pts)
    result = np.asarray(result)
    np.savetxt('results/result.txt', result[:, :4])


def is_valid_pcd(pts):
    # diaobi visible
    label = pts[:, 3]
    diaobi_ratio = sum(label == cls2label['diaoBi']) / len(label)
    if diaobi_ratio < 0.08: return False
    return True


def transform_pseudoPCD_xyz_deltaxyz(img,
                                     in_channels=6,
                                     fill_value=-1,
                                     img_padding=0):
    """将xyz转换为xyz+xyz与中心点的偏移

    Args:
        img (_type_): _description_
        in_channels (int, optional): _description_. Defaults to 6.
        fill_value (int, optional): _description_. Defaults to -1.
        img_padding (int, optional): _description_. Defaults to 0.

    Returns:
        _type_: _description_
    """
    img_shape = list(img.shape)
    new_img = np.full(shape=img_shape[:2] + [in_channels],
                      fill_value=fill_value,
                      dtype=np.float32)
    new_img = new_img.reshape(-1, in_channels)
    # norm
    img = img.reshape(-1, 3)
    valid_mask = np.sum(img, axis=-1) != img_padding * img_shape[2]
    # import pdb
    # pdb.set_trace()
    mean = np.mean(img[valid_mask], axis=0)
    offset = img[valid_mask] - mean
    new_img[valid_mask] = np.concatenate([img[valid_mask], offset], 1)
    new_img = new_img.reshape(img_shape[:2] + [in_channels])
    return new_img


def transform_pseudoPCD_deltaxyz_distance(img,
                                          in_channels=4,
                                          fill_value=-1,
                                          img_padding=0):
    """将xyz转换为xyz与中心点的偏移+点与中心点距离

    Args:
        img (_type_): _description_
        in_channels (int, optional): _description_. Defaults to 6.
        fill_value (int, optional): _description_. Defaults to -1.
        img_padding (int, optional): _description_. Defaults to 0.

    Returns:
        _type_: _description_
    """
    img_shape = list(img.shape)
    new_img = np.full(shape=img_shape[:2] + [in_channels],
                      fill_value=fill_value,
                      dtype=np.float32)
    new_img = new_img.reshape(-1, in_channels)
    # norm
    img = img.reshape(-1, 3)
    valid_mask = np.sum(img, axis=-1) != img_padding * img_shape[2]
    # import pdb
    # pdb.set_trace()
    mean = np.mean(img[valid_mask], axis=0)
    offset = img[valid_mask] - mean
    distance = np.linalg.norm(offset, axis=1).reshape(-1, 1)
    new_img[valid_mask] = np.concatenate([offset, distance], 1)
    new_img = new_img.reshape(img_shape[:2] + [in_channels])
    return new_img


def process_pseudo_6cls_6inchannels(args):
    ignore_label = 6
    img_padding = 0
    fill_value = -1
    in_channels = 6
    resolution, pcd_path, save_img_folder, save_mask_folder, save_vismask_folder = args
    img = np.full((resolution, resolution, 3), img_padding, dtype=np.float32)
    mask = np.full((resolution, resolution), ignore_label, dtype=np.uint8)
    vismask = np.full((resolution, resolution, 3), 255, dtype=np.uint8)
    pcd = np.loadtxt(pcd_path)
    file_name = os.path.splitext(pcd_path)[0].split('/')[-1]
    save_img_path = os.path.join(save_img_folder, file_name + '.npy')
    save_mask_path = os.path.join(save_mask_folder, file_name + '.png')
    save_vismask_path = os.path.join(save_vismask_folder, file_name + '.png')

    # R = _utils.get_rotation_mat(axis='zy', angles=[-np.pi / 2, np.pi / 2])
    # pcd[:, :3] = pcd[:, :3].dot(R.T)
    # pcd = pcd[pcd[:, 2] != 0]
    flag = is_valid_pcd(pcd)
    xmin, ymin = pcd.min(axis=0)[:2]
    xmax, ymax = pcd.max(axis=0)[:2]
    if not flag: return
    num_total = 0
    num_collision = 0
    pixel_collision_dict = DefaultDict(list)
    t1 = time()
    for pt in pcd:

        # coord = np.around(resolution / 2 / np.tan(np.deg2rad(fov / 2)) *
        #                   pt[:2] / pt[2] + resolution / 2).astype(np.int32)
        coordx = round((resolution - 1) * (pt[0] - xmin) / (xmax - xmin))
        coordy = round((resolution - 1) * (pt[1] - ymin) / (ymax - ymin))
        coord = np.array([coordx, coordy], dtype=np.int32)
        if (abs(coord.astype(np.int64)) >= resolution).any():
            continue
        # num_total += 1
        # if sum(img[coord[1], coord[0]]) != img_padding * 3:
        #     num_collision += 1
        #     pixel_collision_dict[f'{coord[1]}_{coord[0]}'].append(pt[:3])
        img[coord[1], coord[0]] = pt[:3]
        mask[coord[1], coord[0]] = int(pt[3])
        vismask[coord[1], coord[0]] = colors_matrix[int(pt[3])]

    # dist_list = []
    # collision_length = []
    # for key in pixel_collision_dict.keys():
    #     pts = np.asarray(pixel_collision_dict[key])
    #     collision_length.append(len(pts))
    #     diff_z = np.max(pts[:, 2]) - np.min(pts[:, 2])
    #     dist_list.append(diff_z)
    # mean = np.mean(pts, 0)
    # distaneces = np.linalg.norm(pts - mean)
    # distanece = np.mean(distaneces)
    # dist_list.append(distanece)
    # mean_dist = np.max(dist_list)
    # dist_list = np.asarray(dist_list)
    # ratio = np.sum(dist_list > 0.5) / len(dist_list)
    # print(num_collision / num_total, ratio, np.mean(collision_length))
    # dilate
    # img = torch.nn.functional.max_pool2d(torch.tensor(img),kernel_size=3,stride=1,)

    img = transform_pseudoPCD_xyz_deltaxyz(img,
                                           in_channels=in_channels,
                                           fill_value=fill_value,
                                           img_padding=img_padding)
    np.save(save_img_path, img)
    cv2.imwrite(save_mask_path, mask)
    cv2.imwrite(save_vismask_path, vismask)


def process_pseudo_4cls_4inchannels(args):
    ignore_label = 4
    img_padding = 0
    fill_value = -1
    in_channels = 4
    resolution, pcd_path, save_img_folder, save_mask_folder, save_vismask_folder = args
    img = np.full((resolution, resolution, 3), img_padding, dtype=np.float32)
    mask = np.full((resolution, resolution), ignore_label, dtype=np.uint8)
    vismask = np.full((resolution, resolution, 3), 255, dtype=np.uint8)
    pcd = np.loadtxt(pcd_path)

    R = _utils.get_rotation_mat(axis='x', angles=[-np.pi / 2])
    pcd[:, :3] = pcd[:, :3] @ R.T

    # transform labels
    labels = pcd[:, 3]
    person_mask = labels == 3
    labels[person_mask] = 0
    shengzi_mask = np.bitwise_or(labels == 4, labels == 5)
    labels[shengzi_mask] = 3
    pcd[:, 3] = labels

    file_name = '_'.join(os.path.splitext(pcd_path)[0].split('/')[-2:])
    save_img_path = os.path.join(save_img_folder, file_name + '.npy')
    save_mask_path = os.path.join(save_mask_folder, file_name + '.png')
    save_vismask_path = os.path.join(save_vismask_folder, file_name + '.png')
    flag = is_valid_pcd(pcd)
    xmin, ymin = pcd.min(axis=0)[:2]
    xmax, ymax = pcd.max(axis=0)[:2]
    if not flag: return

    # 计算坐标映射
    coordx = ((resolution - 1) * (pcd[:, 0] - xmin) /
              (xmax - xmin)).round().astype(np.int32)
    coordy = ((resolution - 1) * (pcd[:, 1] - ymin) /
              (ymax - ymin)).round().astype(np.int32)
    valid_mask = np.logical_and(
        np.abs(coordx) < resolution,
        np.abs(coordy) < resolution)

    # 使用掩码进行处理，避免处理无效坐标
    valid_coords = np.column_stack((coordx[valid_mask], coordy[valid_mask]))
    valid_pts = pcd[valid_mask]

    # 将有效坐标的颜色和深度值写入图像和掩码
    img[valid_coords[:, 1], valid_coords[:, 0]] = valid_pts[:, :3]
    mask[valid_coords[:, 1], valid_coords[:, 0]] = valid_pts[:, 3]
    vismask[valid_coords[:, 1],
            valid_coords[:, 0]] = colors_matrix[valid_pts[:,
                                                          3].astype(np.int8)]

    # img_copy = np.full((resolution, resolution, 3),
    #                    img_padding,
    #                    dtype=np.float32)
    # mask_copy = np.full((resolution, resolution), ignore_label, dtype=np.uint8)
    # for pt in pcd:
    #     # coord = np.around(resolution / 2 / np.tan(np.deg2rad(fov / 2)) *
    #     #                   pt[:2] / pt[2] + resolution / 2).astype(np.int32)
    #     coordx = round((resolution - 1) * (pt[0] - xmin) / (xmax - xmin))
    #     coordy = round((resolution - 1) * (pt[1] - ymin) / (ymax - ymin))
    #     coord = np.array([coordx, coordy], dtype=np.int32)
    #     if (abs(coord.astype(np.int64)) >= resolution).any():
    #         continue
    #     img_copy[coord[1], coord[0]] = pt[:3]
    #     mask_copy[coord[1], coord[0]] = int(pt[3])
    # #     vismask[coord[1], coord[0]] = colors_matrix[int(pt[3])]
    # t2 = time.time()
    # print(t2 - t1)

    img = transform_pseudoPCD_deltaxyz_distance(img,
                                                in_channels=in_channels,
                                                fill_value=fill_value,
                                                img_padding=img_padding)
    np.save(save_img_path, img)
    cv2.imwrite(save_mask_path, mask)
    cv2.imwrite(save_vismask_path, vismask)


colors_matrix = np.array([
    [255, 0, 0],  # 蓝色
    [0, 255, 0],  # 绿色
    [0, 0, 255],  # 红色
    [0, 255, 255],  # 黄色
    [128, 0, 128],  # 紫色
    [255, 255, 0]  # 青色
])


def test_pseudo_img():
    resolution = 224
    fov = 70.4
    pcd_folder1 = 'results/pcd/pcd6/lidar'
    # pcd_folder2 = 'results/pcd/pcd5/lidar'
    # pcd_folder3 = 'results/pcd/pcd5_part2/lidar'
    save_root = 'results/pseudo_data/4cls/4channels_part6_viewXZ'
    save_img_folder = f'{save_root}/imgs'
    save_mask_folder = f'{save_root}/masks'
    save_vismask_folder = f'{save_root}/vis_masks'
    _utils.mkdir_or_exist(save_img_folder)
    _utils.mkdir_or_exist(save_mask_folder)
    _utils.mkdir_or_exist(save_vismask_folder)
    pcd_files = glob(os.path.join(pcd_folder1, '*txt'))
    # pcd_files = ['results/pcd/pcd2/scene0_lidar0.txt']
    arg_list = [(resolution, path, save_img_folder, save_mask_folder,
                 save_vismask_folder) for path in pcd_files]
    for arg in arg_list:
        process_pseudo_4cls_4inchannels(arg)
    # with multiprocessing.Pool(processes=60) as pool:
    #     pool.map(process_pseudo_4cls_4inchannels, arg_list)


def test_R():
    def gen_lines():
        x = np.linspace([0, 0, 0], [0, 0, 10], 1000)
        x = np.concatenate([x, np.full((1000, 1), 0)], 1)
        y = np.linspace([0, 0, 0], [0, 10, 0], 1000)
        y = np.concatenate([y, np.full((1000, 1), 1)], 1)
        z = np.linspace([0, 0, 0], [10, 0, 0], 1000)
        z = np.concatenate([z, np.full((1000, 1), 2)], 1)
        pts = np.concatenate([x, y, z], 0)
        return pts

    scene = np.array([0, 0, 10])
    new_lidar = np.array([10, 0, 10])
    # R, t = _utils.get_lidar_pos_params(p_obj=scene, p_lidar_new=new_lidar)
    R1 = _utils.get_rotation_mat('zy', [np.pi / 4, np.pi / 2])
    R2 = _utils.get_rotation_mat('yz', [-np.pi / 2, -np.pi / 4])
    print(R1.T - R2)

    # pts = gen_lines()
    # out = pts[:, :3] @ R1.T
    # out = np.concatenate([out, pts[:, 3:4]], 1)

    # q = util.QuaternionHandler()
    # v1 = np.array([0, 0, 1])
    # v2 = np.array([0, 1, 0])
    # rq1 = q.getQuaternion(v1, v2)
    # R = q.getRotation(rq1)
    # out = (R2 @ R1).T == R1.T @ R2.T
    # np.savetxt('tmp.txt', out)

from scene_params import LIDAR_ORI_POS
if __name__ == "__main__":
    save_folder = 'results/pcd/pcd9'
    _utils.mkdir_or_exist(save_folder)

    
    # for i in range(0, 30):
    #     test_rendering_scene((i, save_folder))
    test_rendering_scene((1, save_folder))

    # num_cores = os.cpu_count() - 3
    # arg_list = [(i, save_folder) for i in range(0, 200)]
    # with multiprocessing.Pool(processes=num_cores) as pool:
    #     pool.map(test_rendering_scene, arg_list)

    # test_pseudo_img()

    # test_3dshape()

    # test_move_camera()

    # test_R()
    # test_lidar_rotate()
    # test_render_sdf()
