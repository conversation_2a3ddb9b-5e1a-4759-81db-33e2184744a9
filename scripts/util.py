import numpy as np
import torch
import sys
import os
from tqdm import tqdm
from copy import deepcopy
from glob import glob
import math
import pdb
import pickle
from time import time
import multiprocessing

from SDF_models.NeuralPull.model import NPullNetwork
from scipy.spatial.transform import Rotation

np.random.seed()


def calculate_plane_equation(A, B):
    '''
    找出以AB为法向量方向且经过点B的平面方程
    '''
    # 计算直线AB的方向向量
    AB = np.array(B) - np.array(A)
    # 选择一个法向量（这里使用直线AB的方向向量）
    n = AB
    # 平面方程中的点B
    B_point = np.array(B)
    # 构建平面方程
    a, b, c = n
    d = -np.dot(n, B_point)

    return a, b, c, d


def get_polar_rose_grid(viewpoint, plane_center, r=10, num_pt=200000):
    plane_center = np.asarray(plane_center)
    viewpoint = np.asarray(viewpoint)
    # 在xy平面绘制玫瑰线
    scale = 3 * np.sqrt(2)
    pt_list = []
    theta = 0
    for i in range(num_pt):
        radius = np.cos(theta * scale) * r
        theta += 0.001
        x = radius * np.cos(theta)
        y = radius * np.sin(theta)
        pt_list.append([x, y, 0])
    pts = np.asarray(pt_list)
    # 将平面旋转至与视线垂直的平面
    view_ray = plane_center - viewpoint
    rose_ray = np.asarray([0, 0, 1])

    R = quaternion_from_two_vectors(rose_ray, view_ray)
    pts = np.dot(pts, R.T) + plane_center

    return pts


def get_plane_grid(viewpoint, plane_center, size=2, step=0.05):
    '''
    在plane_center处生成网格点,这些网格点分布在以viewpoint与plane_center垂直且穿过plane_center的平面上
    viewpoint
    plane_center: 网格中心点
    size: 网格大小
    resulotion: 网格分辨率
    '''

    AB = np.array(plane_center) - np.array(viewpoint)
    n = AB / np.linalg.norm(AB)

    a, b, c, d = calculate_plane_equation(viewpoint, plane_center)
    # [x,1,1,d]
    x = -(b + c + d) / (a + 1e-5)
    direction_1 = np.array([x, 1, 1]) - np.array(plane_center)
    direction_1 = direction_1 / np.linalg.norm(direction_1)

    direction_2 = np.cross(direction_1, n)

    # 生成网格点的坐标范围
    resulotion = int(size / step)
    x_range = np.linspace(-size / 2, size / 2, resulotion)
    y_range = np.linspace(-size / 2, size / 2, resulotion)

    # 生成网格点
    grid_points = []
    for x in x_range:
        for y in y_range:
            point = plane_center + x * direction_1 + y * direction_2
            grid_points.append(point)

    return torch.vstack(grid_points)


def shift_pts(shift_xyz, pts):
    '''
    平移点
    shift_xyz: xyz 方向平移距离
    '''
    shift = torch.tensor(np.tile(
        np.asarray(shift_xyz).reshape(1, 3), (len(pts), 1)),
                         dtype=torch.float32).cuda()
    pts = pts - shift
    return pts


def get_rotate_matrix(angles_XYZ=[0, 0, 0]):
    '''
    angles_XYZ: XYZ旋转角度 [0,2*np.pi], list
    '''
    alpha, beta, sigma = angles_XYZ
    R_x = torch.tensor([[1, 0, 0], [0, np.cos(alpha), -np.sin(alpha)],
                        [0, np.sin(alpha), np.cos(alpha)]],
                       dtype=torch.float32)
    R_y = torch.tensor([[np.cos(beta), 0, np.sin(beta)], [0, 1, 0],
                        [-np.sin(beta), 0, np.cos(beta)]],
                       dtype=torch.float32)
    R_z = torch.tensor([[np.cos(sigma), -np.sin(sigma), 0],
                        [np.sin(sigma), np.cos(sigma), 0], [0, 0, 1]],
                       dtype=torch.float32)
    R = R_z.mm(R_y).mm(R_x)
    return R


def rotate_pts(ptA, ptsB, angles_XYZ=[0, 0, 0], R=None):
    '''
    点B们绕A随机旋转, 若提供旋转矩阵R,则angles_XYZ失效

    ptA: single pt, torch tensor
    ptsB: multi pts, torch tensor
    angles_XYZ: XYZ旋转角度 [0,2*np.pi], list
    '''
    device = ptsB.device
    shift_ptB = ptsB - ptA
    if R is not None:
        R = R.to(device)
        return ptA + shift_ptB.mm(R.T)

    R = get_rotate_matrix(angles_XYZ=angles_XYZ)
    # device = ptsB.device
    # R = R.to(device)
    # shift_ptB = ptsB - ptA
    R = R.to(device)
    res = shift_ptB.mm(R.T) + ptA
    return res


def rotate_pts_np(ptA, ptsB, angles_XYZ=[0, 0, 0], R=None):
    '''
    点B们绕A随机旋转, 若提供旋转矩阵R,则angles_XYZ失效

    ptA: single pt, torch tensor
    ptsB: multi pts, torch tensor
    angles_XYZ: XYZ旋转角度 [0,2*np.pi], list
    '''
    shift_ptB = ptsB - ptA
    if R is not None:
        return ptA + shift_ptB.dot(R.T)

    R = get_rotate_matrix(angles_XYZ=angles_XYZ)
    res = shift_ptB.dot(R.T) + ptA
    return res


def farthest_point_sample(point, npoint):
    N, D = point.shape
    xyz = point[:, :3]
    centroids = np.zeros((npoint, ))
    distance = np.ones((N, )) * 1e10
    farthest = np.random.randint(0, N)
    for i in range(npoint):
        centroids[i] = farthest
        centroid = xyz[farthest, :]
        dist = np.sum((xyz - centroid)**2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = np.argmax(distance, -1)
    point = point[centroids.astype(np.int32)]
    return point


def load_NeuralPull_model(ckpt_path):
    model = NPullNetwork(d_out=1,
                         d_in=3,
                         d_hidden=256,
                         n_layers=8,
                         skip_in=[4],
                         multires=0,
                         bias=0.5,
                         scale=1.0,
                         geometric_init=True,
                         weight_norm=True)
    ckpt = torch.load(ckpt_path, map_location='cpu')
    flag = model.load_state_dict(ckpt['sdf_network_fine'])
    # print(flag)
    model.eval()
    model = model.cuda()
    return model


def random_pos(x_range, y_range, z_range):
    x = np.random.choice(x_range, 1)[0]
    y = np.random.choice(y_range, 1)[0]
    z = np.random.choice(z_range, 1)[0]
    return np.asarray([x, y, z])


def rendering_line(start_pt, end_pt, num_points=100, noise_std=0.005):
    """rendering line pts

    Args:
        start_pt (_type_): _description_
        end_pt (_type_): _description_
        num_points (int, optional): _description_. Defaults to 100.
        noise_std (float, optional): _description_. Defaults to 0.005.

    Returns:
        _type_: _description_
    """
    line_pts = np.linspace(start_pt, end_pt, num_points)
    # 添加噪声扰动
    noise = np.random.normal(0, noise_std, size=line_pts.shape)  # 生成噪声
    line_pts = line_pts + noise  # 添加噪声
    return line_pts


def quaternion_from_two_vectors(v1, v2):
    '''
    使用四元数实现三维空间两个向量之间的旋转矩阵求解
    从v1旋转到v2的四元数
    '''
    v1 = np.asarray(v1)
    v2 = np.asarray(v2)

    v1 = v1 / np.linalg.norm(v1)
    v2 = v2 / np.linalg.norm(v2)

    dot_product = np.dot(v1, v2)
    if np.isclose(dot_product, 1.0):
        return np.eye(3)  # No rotation needed, return identity matrix

    if np.isclose(dot_product, -1.0):
        # Find an arbitrary orthogonal vector to v1
        axis = np.cross(v1, [1.0, 0.0, 0.0])
        if np.linalg.norm(axis) < 1e-6:
            axis = np.cross(v1, [0.0, 1.0, 0.0])
        axis = axis / np.linalg.norm(axis)
        q = quaternion_from_axis_angle(axis, np.pi)
        R = rotation_matrix_from_quaternion(q)
        return R

    axis = np.cross(v1, v2)
    axis = axis / np.linalg.norm(axis)
    angle = np.arccos(dot_product)
    q = quaternion_from_axis_angle(axis, angle)
    R = rotation_matrix_from_quaternion(q)
    return R


def quaternion_from_axis_angle(axis, angle):
    axis = np.asarray(axis)
    axis = axis / np.linalg.norm(axis)
    half_angle = 0.5 * angle
    w = np.cos(half_angle)
    xyz = axis * np.sin(half_angle)
    return np.array([w, *xyz])


def rotation_matrix_from_quaternion(q):
    '''
    由四元数获取旋转矩阵
    '''
    q = np.asarray(q)
    q = q / np.linalg.norm(q)
    w, x, y, z = q
    return np.array(
        [[1 - 2 * (y**2 + z**2), 2 * (x * y - w * z), 2 * (x * z + w * y)],
         [2 * (x * y + w * z), 1 - 2 * (x**2 + z**2), 2 * (y * z - w * x)],
         [2 * (x * z - w * y), 2 * (y * z + w * x), 1 - 2 * (x**2 + y**2)]])


def get_NeuralPull_ckpt_paths():
    ckpt_paths = glob(
        '/home/<USER>/workspace/3D/NeuralPull-Pytorch/outs/shapenet/*/checkpoints/*'
    )
    return ckpt_paths


def sample_points(input_volume,
                  sampled_points,
                  grid_coord_ori=(-1, -1, -1),
                  sdf_length=2.0,
                  resolution=256):
    """sample point sdf from grid

    Args:
        input_volume (tensor): input voxel sdfs
        sampled_points (list): pt in world coordinate system 
        grid_coord_ori (tuple, optional): grid 在世界坐标系的坐标原点. Defaults to (-1, -1, -1).
        sdf_length (float, optional): grid 边长,默认xyz一致. Defaults to 2.0.
        resolution (int, optional): grid网格数. Defaults to 256.

    Returns:
        _type_: 采样点sdf值
    """

    sampled_points = sampled_points.reshape(-1, 3)
    grid_coord_ori = grid_coord_ori.reshape(1, 3)

    # 将坐标点转换至grid坐标
    grid_coord = (sampled_points - grid_coord_ori) / (sdf_length /
                                                      (resolution - 1))

    # grid坐标归一化坐标 [-1, 1]
    normalized_points = grid_coord * 2.0 / torch.tensor(
        [resolution - 1, resolution - 1, resolution - 1]) - 1.0
    grid = normalized_points.reshape(1, -1, 1, 1, 3)[..., [2, 1, 0]].float()
    sampled_values = torch.nn.functional.grid_sample(input_volume,
                                                     grid,
                                                     mode='bilinear',
                                                     align_corners=True,
                                                     padding_mode="border")

    return sampled_values


def sample_points_worker(args):
    sampled_values = sample_points(*args)
    return pickle.dumps(sampled_values)


def get_grid_uniform(resolution=256, sdf_length=2):
    '''
    resolution: number of grid
    sdf_length: range of sdf function
    '''
    x = np.linspace(-sdf_length / 2, sdf_length / 2, resolution)
    # x = torch.linspace(-1., 1., resolution)
    y = x
    z = x

    xx, yy, zz = np.meshgrid(x, y, z, indexing='ij')
    grid_points_mnp = np.stack([xx, yy, zz], -1)  # MNP
    grid_points_n3 = grid_points_mnp.reshape(-1, 3)
    return grid_points_mnp, grid_points_n3


def ray_cube_intersection(ray_origin, ray_direction, cube_min, cube_max):
    """有向射线与立方体交点计算,若无交点则返回None

    Args:
        ray_origin (_type_): 射线起点
        ray_direction (_type_): 归一化的射线方向
        cube_min (_type_): 立方体最小值
        cube_max (_type_): 立方体最大值

    Returns:
        两个交点/None
    """
    tmin = float("-inf")  # 初始的最小交点参数设为负无穷
    tmax = float("inf")  # 初始的最大交点参数设为正无穷

    # 对于x、y、z三个轴分别进行处理
    for i in range(3):
        if ray_direction[i] == 0:
            if ray_origin[i] < cube_min[i] or ray_origin[i] > cube_max[i]:
                return None  # 射线平行于面且在立方体外部，不相交
        else:
            t1 = (cube_min[i] -
                  ray_origin[i]) / ray_direction[i]  # 计算与最小面的交点参数
            t2 = (cube_max[i] -
                  ray_origin[i]) / ray_direction[i]  # 计算与最大面的交点参数
            tmin = max(tmin, min(t1, t2))  # 更新最小交点参数
            tmax = min(tmax, max(t1, t2))  # 更新最大交点参数

    if tmax >= tmin >= 0:
        # 射线与立方体相交，计算交点坐标
        intersection_point1 = [
            ray_origin[i] + tmin * ray_direction[i] for i in range(3)
        ]
        intersection_point2 = [
            ray_origin[i] + tmax * ray_direction[i] for i in range(3)
        ]
        return intersection_point1, intersection_point2  # 返回两个交点
    else:
        return None  # 射线与立方体不相交


def cal_scan_radius_by_fov(fov, viewpoint, plane_center):
    """以及视点与扫描平面中心之间距离与FOV计算出扫描平面半径

    Args:
        fov (float): FOV角度,M70默认70.4
        viewpoint (list): 视点
        plane_center (list): 场景渲染中心
    """
    assert 0 < fov < 180
    dist = np.array(viewpoint) - np.array(plane_center)
    dist = np.linalg.norm(dist)
    fov = math.radians(fov)
    return dist * math.tan(fov / 2)


def infer_grid_sdfs(model, grid_pts_tensor, resolution, batch=200000):
    """计算grid立方体所有sdf值

    Args:
        model (_type_): sdf model
        grid_pts_tensor (_type_): grid coords
        resolution (_type_): grid resolution
        batch (int, optional): infer batch. Defaults to 200000.

    Returns:
        _type_: sdf matrix
    """
    dist = []
    sections = len(grid_pts_tensor) // batch
    with torch.no_grad():
        for pts in np.array_split(grid_pts_tensor, sections):
            out = model(pts)
            dist.append(out)
    dist = torch.cat(dist, 0)
    dist = dist.reshape(1, 1, resolution, resolution, resolution)
    return dist


def get_local_samples(start_pt, target_pt, step):
    """在两点之间采样点

    Args:
        start_pt (_type_): 采样起点
        target_pt (_type_): 采样终点
        step (_type_): 采样点间距

    Returns:
        _type_: 采样点
    """
    ray_length = np.linalg.norm(target_pt - start_pt)
    num = int(ray_length / step)
    samples = np.linspace(start_pt, target_pt, num=num)
    return samples


def rendering_worker(args):
    """单进程渲染worker

    Args:
        args (tuple): (direction_pts_tensor, obj_list, VIEWPOINT_tensor, STEP)
    """
    direction_pts_tensor, obj_list, VIEWPOINT_tensor, STEP = args
    # 计算采样点
    ray_samples_R_list = []  # 记录旋转物体后每根射线的采样点
    ray_samples_ori_list = []  # 记录原射线采样点
    size_list = []  # 记录一段射线采样点数
    ray_samples_label_list = []

    t1_list = []
    t2_list = []
    for idx, pt in enumerate(direction_pts_tensor):
        obj_samples_ori = []
        obj_samples_R = []
        obj_samples_label = []
        # t1 = time()
        for obj in obj_list:
            grid_min, grid_max = obj.get_grid_bound()

            # 考虑物体旋转(通过ray旋转实现)

            ray_pts = torch.cat([VIEWPOINT_tensor, pt.reshape(1, 3)], 0)
            ray_pts_R = rotate_pts(obj.obj_center,
                                   ray_pts,
                                   angles_XYZ=obj.obj_angles).numpy()

            ray_D = (ray_pts_R[1] - ray_pts_R[0]
                     ) / np.linalg.norm(ray_pts_R[1] - ray_pts_R[0])
            intersection_pts = ray_cube_intersection(ray_origin=ray_pts_R[0],
                                                     ray_direction=ray_D,
                                                     cube_min=grid_min,
                                                     cube_max=grid_max)

            if intersection_pts is None: continue
            intersection_pts = np.array(intersection_pts)
            # 旋转射线后的采样点
            samples_R = torch.tensor(
                get_local_samples(intersection_pts[0],
                                  intersection_pts[1],
                                  step=STEP))

            # 记录实际射线采样点
            R_inverse = get_rotate_matrix(angles_XYZ=-np.array(obj.obj_angles))
            samples_ori = rotate_pts(obj.obj_center, samples_R, R=R_inverse)
            obj_samples_R.extend(samples_R)
            obj_samples_ori.extend(samples_ori)

            # 记录采样点类别
            obj_samples_label.extend([obj.label] * len(samples_ori))
        # t2 = time()
        if len(obj_samples_ori) == 0: continue
        obj_samples_R = torch.stack(obj_samples_R)
        obj_samples_ori = torch.stack(obj_samples_ori)
        obj_samples_label = torch.tensor(obj_samples_label)
        ray_samples_R_list.append(obj_samples_R)
        ray_samples_ori_list.append(obj_samples_ori)
        size_list.append(len(obj_samples_ori))
        ray_samples_label_list.append(obj_samples_label)
        # t3 = time()
        # t1_list.append(t2 - t1)
        # t2_list.append(t3 - t2)
        # if idx % 100 == 0:
        #     print(idx,
        #           sum(t1_list) / len(t1_list),
        #           sum(t2_list) / len(t2_list))
    serialized_data = pickle.dumps([
        ray_samples_R_list, ray_samples_ori_list, size_list,
        ray_samples_label_list
    ])

    return serialized_data


def cal_grid_sdfs(ckpt, save_path, resolution=128):
    _, grid_pts = get_grid_uniform(resolution, sdf_length=2.0)
    grid_pts_tensor = torch.tensor(grid_pts, dtype=torch.float32).cuda()
    # model = load_IGR_model('SDF_models/IGR/100000.pth')
    model = load_NeuralPull_model(ckpt)
    dist = infer_grid_sdfs(model=model,
                           grid_pts_tensor=grid_pts_tensor,
                           resolution=resolution)
    torch.save(dist, save_path)


def rendering_ground_plane():
    """渲染地平面
    """
    pass


def rendering_single_obj(resolution=128,
                         sdf_length=2.0,
                         step=0.01,
                         viewpoint=[0, 5, 0],
                         obj_center=[0, 0, 0],
                         angles=[0, 0, 0],
                         grid_sdfs=None,
                         obj_boundary_sdf_threshold=0.01,
                         fov=70.4,
                         num_scan_ray=200000):
    """渲染单个物体

    Args:
        resolution (int, optional): 网格分辨率. Defaults to 128.
        sdf_length (float, optional): 渲染物体尺寸. Defaults to 2.0.
        step (float, optional): 射线间隔. Defaults to 0.01.
        viewpoint (list, optional): 视点. Defaults to [0, 5, 0].
        obj_center (list, optional): 物体中心坐标. Defaults to [0, 0, 0].
        angles (list, optional): 物体旋转角度. Defaults to [0, 0, 0].
        grid_sdfs (_type_, optional): 物体grid sdf. Defaults to None.
        obj_boundary_sdf_threshold (float, optional): 表面sdf阈值. Defaults to 0.01.
        fov (float): 视野大小
        num_scan_ray (int): 雷达扫描射线数

    Returns:
        _type_: pcd(array)
    """
    grid_coord_ori = torch.tensor(obj_center) - sdf_length / 2
    visible_pts = []
    samples_list = []
    size_list = []
    t1 = time()
    scan_radius = cal_scan_radius_by_fov(fov,
                                         viewpoint=viewpoint,
                                         plane_center=obj_center)
    direction_pts = get_polar_rose_grid(viewpoint=viewpoint,
                                        plane_center=obj_center,
                                        r=scan_radius,
                                        num_pt=num_scan_ray)
    direction_pts_tensor = torch.tensor(direction_pts)
    # rotate rays
    ray_pts = torch.cat(
        [torch.tensor(viewpoint).reshape(-1, 3), direction_pts_tensor],
        0).float()

    ray_pts_R = rotate_pts(torch.tensor(obj_center),
                           ray_pts,
                           angles_XYZ=angles)
    vp_R = ray_pts_R[0].numpy()
    direction_pts_R = ray_pts_R[1:].float().numpy()

    obj_center_np = np.asarray(obj_center)
    # sample pts in grid
    for pt in direction_pts_R:
        # cal intersections of ray and cube
        ray_D = (pt - vp_R) / np.linalg.norm(pt - vp_R)
        cube_min = obj_center_np - sdf_length / 2
        cube_max = obj_center_np + sdf_length / 2
        intersection_pts = ray_cube_intersection(ray_origin=vp_R,
                                                 ray_direction=ray_D,
                                                 cube_min=cube_min,
                                                 cube_max=cube_max)
        if intersection_pts is None: continue
        intersection_pts = np.array(intersection_pts)
        samples = get_local_samples(intersection_pts[0],
                                    intersection_pts[1],
                                    step=step)
        samples = torch.tensor(samples).float()
        samples_list.append(samples)
        size_list.append(len(samples))
    t2 = time()

    all_samples = torch.cat(samples_list, dim=0)
    print('sample time', t2 - t1)
    sampled_sdfs = sample_points(input_volume=grid_sdfs,
                                 sampled_points=all_samples,
                                 resolution=resolution,
                                 grid_coord_ori=grid_coord_ori,
                                 sdf_length=sdf_length)
    t3 = time()
    print('grid sample time', t3 - t2)
    sampled_sdfs = sampled_sdfs.flatten()
    indices_split = []
    for i, s in enumerate(size_list):
        if i == 0:
            indices_split.append(s)
        else:
            indices_split.append(indices_split[-1] + s)
    sampled_sdfs_list = np.array_split(sampled_sdfs, indices_split)
    visible_pts = []
    for samples, sdfs in zip(samples_list, sampled_sdfs_list):
        indices = torch.arange(len(sdfs))
        negative_mask = sdfs <= obj_boundary_sdf_threshold
        # 注释掉的代码虽可减少噪点但又有新噪点
        # mask = sdfs[1:] <= sdfs[:-1]
        # negative_mask[:-1] = negative_mask[:-1] & mask
        if not torch.any(negative_mask): continue
        intersection_idx = torch.min(indices[negative_mask])
        if intersection_idx == 0: continue
        # 通过插值方式获取精确交点位置
        val1 = sdfs[intersection_idx - 1]
        val2 = -sdfs[intersection_idx]
        lamb1 = val2 / (val1 + val2)
        lamb2 = 1 - lamb1
        # 若obj被旋转了,需要将采样点反旋转回去
        if np.sum(angles) != 0:
            R_inverse = get_rotate_matrix(angles_XYZ=-np.array(angles))
            samples = rotate_pts(torch.tensor(obj_center, dtype=torch.float32),
                                 samples,
                                 R=R_inverse)
        inter_pt = lamb1 * samples[intersection_idx -
                                   1] + lamb2 * samples[intersection_idx]
        visible_pts.append(inter_pt)

    visible_pts = torch.stack(visible_pts)
    visible_pts = visible_pts.numpy()
    return visible_pts


def rendering_multi_obj(resolution=128,
                        step=0.01,
                        viewpoint=[0, 5, 0],
                        scene_center=[0, 0, 0],
                        obj_list=[],
                        obj_boundary_sdf_threshold=0.0,
                        fov=70.4,
                        num_scan_ray=200000,
                        use_rose=True):
    """渲染多个物体

    Args:
        resolution (int, optional): 网格分辨率. Defaults to 128.
        step (float, optional): ray采样点间隔. Defaults to 0.01.
        viewpoint (list, optional): 视点. Defaults to [0, 5, 0].
        scene_center (list, optional): 玫瑰曲线中心. Defaults to [0, 0, 0].
        obj_list (list, optional): 渲染物. Defaults to [].
        obj_boundary_sdf_threshold (float, optional): 物体边界sdf值. Defaults to 0.0.
        fov (float, optional): 视野. Defaults to 70.4.
        num_scan_ray (int, optional): 扫描射线数. Defaults to 200000.

    Returns:
        _type_: _description_
    """
    t1 = time()
    viewpoint_tensor = torch.tensor(viewpoint).reshape(1, 3).float()
    if use_rose:
        scan_radius = cal_scan_radius_by_fov(fov,
                                             viewpoint=viewpoint,
                                             plane_center=scene_center)
        # 构造扫描点
        direction_pts = get_polar_rose_grid(viewpoint=viewpoint,
                                            plane_center=scene_center,
                                            r=scan_radius,
                                            num_pt=num_scan_ray)
        direction_pts_tensor = torch.tensor(direction_pts).float()
    else:
        assert len(obj_list) == 1  #
        direction_pts_tensor = get_plane_grid(viewpoint=viewpoint,
                                              plane_center=scene_center,
                                              size=obj_list[0].obj_size,
                                              step=0.05).float()
    # np.savetxt('scripts/results/rose.txt', direction_pts)
    t2 = time()
    # print(f'rose:{t2-t1}')
    # 计算采样点
    ray_samples_R_list = []  # 记录旋转物体后每根射线的采样点
    ray_samples_ori_list = []  # 记录原射线采样点
    size_list = []

    if num_scan_ray < 5000:
        args = (direction_pts_tensor, obj_list, viewpoint_tensor, step)
        ray_samples_R_list, ray_samples_ori_list, size_list, ray_samples_label_list = pickle.loads(
            rendering_worker(args=args))
    else:
        direction_pts_tensor_list = np.array_split(direction_pts_tensor,
                                                   num_scan_ray // 1000)
        arg_list = [(data, obj_list, viewpoint_tensor, step)
                    for data in direction_pts_tensor_list]
        num_cores = min(os.cpu_count(), 60)
        t2_1 = time()
        with multiprocessing.Pool(processes=num_cores) as pool:
            results = pool.map(rendering_worker, arg_list)
        t2_2 = time()
        ray_samples_R_list = []
        ray_samples_ori_list = []
        size_list = []
        ray_samples_label_list = []
        for out in results:
            out = pickle.loads(out)
            ray_samples_R_list.extend(out[0])
            ray_samples_ori_list.extend(out[1])
            size_list.extend(out[2])
            ray_samples_label_list.extend(out[3])
        t2_3 = time()
        # print(f'pool:{t2_2-t2_1},{t2_3-t2_2}')

    t3 = time()
    if len(ray_samples_R_list) == 0: return np.zeros((0, 4))
    all_samples_R = torch.cat(ray_samples_R_list, 0)
    print(f'samples prepared done {t3-t2}, {len(all_samples_R)} samples')
    ray_samples_sdf_list = []

    if len(obj_list) > 1:
        arg_list = [[
            obj.grid_sdf_matrix, all_samples_R,
            obj.get_grid_bound()[0], obj.obj_size, resolution
        ] for obj in obj_list]
        with multiprocessing.Pool(processes=num_cores) as pool:
            results = pool.map(sample_points_worker, arg_list)
        for out in results:
            ray_samples_sdf_list.append(pickle.loads(out).flatten())
    else:
        for obj in obj_list:
            sampled_sdfs = sample_points(
                input_volume=obj.grid_sdf_matrix,
                sampled_points=all_samples_R,
                resolution=resolution,
                grid_coord_ori=obj.get_grid_bound()[0],
                sdf_length=obj.obj_size)
            ray_samples_sdf_list.append(sampled_sdfs.flatten())
    t4 = time()
    print(f'grid sample done {t4-t3}')
    # min(pt sdfs)获取多物体grid采样下点的有效正负性
    ray_samples_sdf_tensor = torch.min(torch.vstack(ray_samples_sdf_list),
                                       0)[0]
    indices_split = []
    for i, s in enumerate(size_list):
        if i == 0:
            indices_split.append(s)
        else:
            indices_split.append(indices_split[-1] + s)
    sampled_sdfs_list = np.array_split(ray_samples_sdf_tensor, indices_split)
    visible_pts = []

    for samples, sdfs, labels in zip(ray_samples_ori_list, sampled_sdfs_list,
                                     ray_samples_label_list):
        indices = torch.arange(len(sdfs))
        negative_mask = sdfs <= obj_boundary_sdf_threshold
        if not torch.any(negative_mask): continue
        intersection_idx = torch.min(indices[negative_mask])
        if intersection_idx == 0: continue
        # 通过插值方式获取精确交点位置
        val1 = sdfs[intersection_idx - 1]
        val2 = -sdfs[intersection_idx]
        lamb1 = val2 / (val1 + val2)
        lamb2 = 1 - lamb1
        inter_pt = lamb1 * samples[intersection_idx -
                                   1] + lamb2 * samples[intersection_idx]
        inter_pt = torch.hstack([inter_pt, labels[intersection_idx]])
        visible_pts.append(inter_pt)
    t5 = time()
    # print(f'collect res:{t5-t4}')
    if len(visible_pts) == 0: return np.zeros((0, 4))
    visible_pts = torch.stack(visible_pts)
    visible_pts = visible_pts.numpy()
    return visible_pts


def get_link_point(viewpoint, num_lines, obj, resolution=128):
    """找出在指定视点下物体合适的链接点

    Args:
        viewpoint (_type_): 吊臂顶点坐标或吊绳分叉点
        num_lines (_type_): 吊绳个数
        resolution (_type_): 网格分辨率,默认128
        obj (PCDMaterial): PCDMaterial object

    Returns:
        _type_: 符合条件的吊点/None
    """
    fov = np.arctan(obj.obj_size / 2 / np.linalg.norm(
        np.array(viewpoint) - np.array(obj.obj_center))) * 2 * 180 / np.pi
    visible_pts = rendering_multi_obj(resolution=resolution,
                                      step=0.01,
                                      viewpoint=viewpoint,
                                      scene_center=obj.obj_center,
                                      obj_list=[obj],
                                      obj_boundary_sdf_threshold=0.0,
                                      fov=fov,
                                      num_scan_ray=30000)
    visible_pts = visible_pts[:, :-1]

    if len(visible_pts) < num_lines:
        print('can not find enough lifting pts')
        return None
    liftingPts = farthest_point_sample(visible_pts, num_lines)

    return liftingPts


# 四元数与旋转矩阵之间的转换
model = 'zyx'


# model = 'xyz'
class QuaternionHandler:
    """
    四元数与旋转矩阵、欧拉角之间的转换
    """

    def __init__(self):
        pass

    # 已知 起始向量 最终向量，获取旋转的四元数
    # 要注意的要点：
    # 1. 算中心轴的方向：toVector 叉乘 fromVector
    # 2. 绕轴转的方向，以逆时针为正方向
    # 3. 四元数的组成 [Ux*sin(theta/2), Uy*sin(theta/2), Uz*sin(theta/2), cos(theta/2)]
    # 4. [Ux, Uy, Uz]必须是单位向量
    def getQuaternion(self, fromVector, toVector):
        """_summary_

        Args:
            fromVector (_type_): _description_
            toVector (_type_): _description_

        Returns:
            _type_: _description_
        """
        fromVector = np.array(fromVector)
        fromVector_e = fromVector / np.linalg.norm(fromVector)

        toVector = np.array(toVector)
        toVector_e = toVector / np.linalg.norm(toVector)

        cross = np.cross(fromVector_e, toVector_e)

        cross_e = cross / np.linalg.norm(cross)

        dot = np.dot(fromVector_e, toVector_e)

        angle = math.acos(dot)

        if angle == 0 or angle == math.pi:
            # print("两个向量处于一条直线")
            return [angle]
        else:
            return [
                cross_e[0] * math.sin(angle / 2),
                cross_e[1] * math.sin(angle / 2),
                cross_e[2] * math.sin(angle / 2),
                math.cos(angle / 2)
            ]

    # 已知四元数，求欧拉角
    def getEuler(self, quaternion):
        rotationClass = self.getRotationClass(quaternion)
        euler = rotationClass.as_euler(model, degrees=False)
        # euler = rotationClass.as_euler(model, degrees=True)
        return euler

    # 已知四元数，求旋转矩阵
    def getRotationClass(self, quaternion):
        rotation = Rotation.from_quat(quaternion)
        return rotation

    def getRotation(self, quaternion):
        rotationClass = self.getRotationClass(quaternion)
        return rotationClass.as_matrix()

def get_rotation_matrix_from_vectors(from_vector, to_vector):
    """
    计算从 from_vector 到 to_vector 的旋转矩阵。
    这个函数能够稳健地处理所有边缘情况（例如向量平行或反向平行）。

    Args:
        from_vector (np.ndarray): 起始向量。
        to_vector (np.ndarray): 目标向量。

    Returns:
        np.ndarray: 3x3 的旋转矩阵。
    """
    # align_vectors 寻找一个旋转R，使得 R * b = a
    # 所以，如果我们要将 from_vector 旋转到 to_vector，即 R * from_vector = to_vector
    # 那么参数应该是 (to_vector, from_vector)
    # rotation, _ = Rotation.align_vectors(to_vector, from_vector)  # Python 3.8+
    rotation = Rotation.align_vectors(to_vector, from_vector)[0] # Older Python versions
    
    return rotation.as_matrix()
