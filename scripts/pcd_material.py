import numpy as np
import torch
from scripts import util


class PCDMaterial:

    def __init__(self,
                 grid_sdf_path,
                 obj_center,
                 obj_scale=2.0,
                 obj_angles=[0, 0, 0],
                 label=0,
                 update_center=True):
        """object info
        雷达坐标系/世界坐标系:x(right),y(down),z(front)
    
        Args:
            grid_sdf_path (_type_): SDF gird值矩阵地址
            obj_center (_type_): 物体在世界坐标系下中心坐标
            obj_scale (float, optional): 物体大小缩放系数. Defaults to 2.0.
            obj_angles (list, optional): 物体旋转角度. Defaults to [0, 0, 0].
            label (int): 物体类别 
            update_center (bool): 是否更新物体中心高度使得物体在平面上
        """
        data = torch.load(grid_sdf_path, map_location='cpu')
        if isinstance(data, dict):
            self.grid_sdf_matrix = torch.tensor(data['grid_tensor'],
                                                dtype=torch.float32)
            xyz_size = np.array(data['max_bound'] - data['min_bound'],
                                dtype=np.float32)
        else:
            self.grid_sdf_matrix = data
            xyz_size = np.array([2, 2, 2], dtype=np.float32)
        self.obj_size = xyz_size * obj_scale
        self.obj_center = torch.tensor(obj_center, dtype=torch.float32)
        if update_center:
            self.update_obj_center()
        self.obj_angles = torch.tensor(obj_angles, dtype=torch.float32)
        self.label = label

    def update_obj_center(self):
        """更新物体中心高度使得物体在地面上(默认地面为y=0)
        """
        self.obj_center[1] = -self.obj_size[1] / 2

    def get_grid_bound(self):
        """依据物体目标大小与中心偏移计算出grid原点在世界坐标下最小最大点

        Returns:
            _type_: _description_
        """
        grid_min = self.obj_center - self.obj_size / 2
        grid_max = self.obj_center + self.obj_size / 2
        return grid_min, grid_max


class DiaoBiMaterial(PCDMaterial):

    def __init__(self,
                 grid_sdf_path,
                 obj_center,
                 obj_scale=2,
                 obj_angles=[0, 0, 0],
                 label=0):
        super().__init__(grid_sdf_path, obj_center, obj_scale, obj_angles,
                         label)

    def get_arm_top_coord(self, viewpoint=[-5, 0, 0]):
        """计算吊臂顶点
        注: 需要将视点设置为正对吊臂位置

        Args:
            viewpoint (list, optional): _description_. Defaults to [-5, 0, 0].

        Returns:
            _type_: _description_
        """
        vp_tensor = torch.tensor(viewpoint).reshape((1, 3))
        vp_tensor = util.rotate_pts((self.obj_center).float(),
                                    vp_tensor.float(), self.obj_angles)
        vp = (vp_tensor.flatten() * self.obj_size / 2 +
              self.obj_center).numpy()
        fov = np.arctan(
            self.obj_size / 2 /
            np.linalg.norm(vp - np.array(self.obj_center))) * 2 * 180 / np.pi
        visible_pts = util.rendering_multi_obj(resolution=128,
                                               viewpoint=vp,
                                               scene_center=self.obj_center,
                                               obj_list=[self],
                                               obj_boundary_sdf_threshold=0,
                                               fov=fov,
                                               num_scan_ray=30000,
                                               use_rose=True)
        # np.savetxt('scripts/results/arm_top.txt', visible_pts)
        indices = np.argsort(visible_pts[:, 0] - visible_pts[:, 2])
        # idx = np.random.choice(20, 1)
        return visible_pts[indices[0]][:-1]
