import numpy as np
import torch
from tqdm import tqdm
from copy import deepcopy
from time import time
import sys
import os
import pdb
import multiprocessing
import pickle



current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(os.path.dirname(current_file_path))
sys.path.append(current_dir)


from SDF_models.IGR.model import ImplicitNet
from scripts import util
from scripts import deprecated_tools
from pcd_material import PCDMaterial, DiaoBiMaterial
from scene import Scene, GridObject, Lidar

cls2label = {
    'bg': 0,
    'diaoWu': 1,
    "diaoBi": 2,
    'person': 3,
    'diaoShen': 4,
    'qianYinShen': 5
}


def test_single_shape_rendering():
    pcd_path = '/home/<USER>/workspace/3D/IGR/data/shapnet/1a6a520652aa2244146fa8a09fad6c38.txt'
    ckpt_path = 'exps/single_shape/2023_07_11_22_12_39/checkpoints/ModelParameters/100000.pth'
    max_marching_steps = 10
    viewpoint = [0.0, 3, 3]
    pts = np.loadtxt(pcd_path)[:, :3]
    resolution = 512
    min_bound = np.asarray([-1, -1, -1])
    max_bound = np.asarray([1, 1, 1])
    model = ImplicitNet(d_in=3,
                        dims=[512, 512, 512, 512, 512, 512, 512, 512],
                        skip_in=[4],
                        geometric_init=True,
                        radius_init=1,
                        beta=100)
    ckpt = torch.load(ckpt_path, map_location='cpu')
    model.load_state_dict(ckpt['model_state_dict'])
    model.eval()
    model = model.cuda()
    deprecated_tools.get_single_shape_visible_pcd(max_marching_steps,
                                                  viewpoint, min_bound,
                                                  max_bound, resolution, model)


def test_multi_shape_rendering():
    pcd_path = '/home/<USER>/workspace/3D/IGR/data/shapnet/1a6a520652aa2244146fa8a09fad6c38.txt'
    ckpt_path = 'exps/single_shape/2023_07_11_22_12_39/checkpoints/ModelParameters/100000.pth'
    max_marching_steps = 20
    viewpoint = [6, 6, 0]
    pts = np.loadtxt(pcd_path)[:, :3]
    pts_2 = deepcopy(pts)
    pts_2[:, 0] += 1.8
    shape_shift_list = [[0, 0, 0], [1.8, 0, 0], [0, 2, 2]]
    shape_rotate_list = [
        util.get_rotate_matrix([np.pi / 2, 0, 0]),
        util.get_rotate_matrix([0, 0, 0]),
        util.get_rotate_matrix([np.pi / 3, np.pi / 3, 0])
    ]
    shape_scale_list = [2, 1, 1.5]
    merged_pts = np.concatenate([pts, pts_2], 0)
    # np.savetxt('scripts/results/tmp.txt', merged_pts)
    resolution = 512
    min_bound = np.asarray([-1, -3, -3])
    max_bound = np.asarray([3, 3, 3])
    model = ImplicitNet(d_in=3,
                        dims=[512, 512, 512, 512, 512, 512, 512, 512],
                        skip_in=[4],
                        geometric_init=True,
                        radius_init=1,
                        beta=100)
    ckpt = torch.load(ckpt_path, map_location='cpu')
    model.load_state_dict(ckpt['model_state_dict'])
    model.eval()
    model = model.cuda()
    # model_2 = deepcopy(model)

    pts = deprecated_tools.get_multi_shape_visible_pcd(
        max_marching_steps, viewpoint, min_bound, max_bound, resolution,
        [model, model, model], shape_shift_list, shape_rotate_list,
        shape_scale_list)
    print(f"rendering {len(pts)} points")
    np.savetxt('scripts/results/multi.txt', pts)


def test_get_liftingpts():
    from scripts.util import load_NeuralPull_model, load_IGR_model
    ckpt_path = '/home/<USER>/workspace/3D/NeuralPull-Pytorch/outs/shapenet/1a4ef4a2a639f172f13d1237e1429e9e/checkpoints/ckpt_040000.pth'
    model = load_NeuralPull_model(ckpt_path)

    # model = load_IGR_model('SDF_models/IGR/100000.pth')
    pts = deprecated_tools.get_diaoWu_liftingPoint(
        diaoWuCenter_pt=np.asarray([5, 5, 5]),
        apexJib_pt=np.asarray([5, 5, 5.4]),
        SDF_model_diaowu=model,
        num_lines=4)
    print(pts)


def rendering_whole_shape():
    obj = DiaoBiMaterial(grid_sdf_path='SDF_models/grid_sdfs/diaobi/diaobi.pt',
                         obj_center=[0, 0, 0],
                         obj_size=1,
                         obj_angles=[0, 0, 0],
                         label=cls2label['diaoWu'])
    vp_list = [[7, 0, 0], [0, -7, 0], [-7, 0, 0], [0, 7, 0], [0, 0, -7],
               [0, 0, 7]]
    pts_list = []
    for vp in vp_list:
        pts = util.rendering_multi_obj(viewpoint=vp,
                                       obj_list=[obj],
                                       num_scan_ray=100000)
        pts_list.append(pts)
    pts = np.concatenate(pts_list, 0)
    print(len(pts))
    np.savetxt('scripts/results/whole_pcd.txt', pts)


def test_gen_plane_grid():
    vp = np.asarray([0, 0, 0])
    plane_center = np.asarray([1, 0, 0])
    pts = deprecated_tools.get_plane_grid(viewpoint=vp,
                                          plane_center=plane_center,
                                          resulotion=128,
                                          size=3)
    line = util.rendering_line(vp, plane_center, 100)
    pts = np.concatenate([pts, line], 0)

    # new_plane_center = np.asarray([1, 1, 0])
    # v1 = plane_center - vp
    # v2 = new_plane_center - vp
    # q = quaternion_from_two_vectors(v1, v2)
    # R = rotation_matrix_from_quaternion(q)
    # pts = pts.dot(R)

    np.savetxt('scripts/results/grid_points.txt', pts)


def test_plot_polar_rose():
    vp = np.asarray([0, 10, 10])
    plane_center = np.asarray([10, 10, 0])
    r = util.cal_scan_radius_by_fov(70.4, vp, plane_center)
    pts = util.get_polar_rose_grid(viewpoint=vp,
                                   plane_center=plane_center,
                                   r=r,
                                   num_pt=10000)
    line = util.rendering_line(vp, plane_center, 100)
    pts = np.concatenate([pts, line], 0)
    np.savetxt('scripts/results/rose.txt', pts)


def test_sample_points():
    # 示例使用
    # 假设你有一个输入体积数据input_volume，形状为(batch_size, channels, volume_depth, volume_height, volume_width)
    # 采样的点的坐标为一个列表sampled_points，每个元素是一个三维坐标(x, y, z)
    # input_volume = torch.arange(1 * 128 * 128 * 128,
    #                             dtype=torch.float32).reshape(
    #                                 1, 1, 128, 128, 128)  # 示例输入体积数据

    sampled_points = [(64, 64, 16), (96, 32, 8)]  # 示例采样点坐标

    pts = torch.arange(128 * 128 * 128 * 3, dtype=torch.float32).reshape(-1, 3)
    input_volume = util.construct_input_volume(pts, [1, 3, 128, 128, 128],
                                               value=1)

    sampled_values = util.sample_points(input_volume, sampled_points)
    print(sampled_values)


def test_ray_cube_visible_pts():
    """测试依靠sdf正负性渲染可见点云,仅测试单个物体渲染
    """
    resolution = 128
    sdf_length = 2.0  # 直接通过sdf_length可控制缩放
    step = 0.01  # ray采样间隔
    viewpoint = [0, 5, 5]
    obj_center = [0, 0, 0]
    angles = [0, 0, 0]
    obj_boundary_sdf_threshold = 0.01

    # 默认训练SDF的数据都缩放至[-1,1]
    _, grid_pts = util.get_grid_uniform(resolution, sdf_length=2.0)
    grid_pts_tensor = torch.tensor(grid_pts, dtype=torch.float32).cuda()
    # model = load_IGR_model('SDF_models/IGR/100000.pth')
    model = util.load_NeuralPull_model(
        '/home/<USER>/workspace/3D/NeuralPull-Pytorch/outs/chair/checkpoints/ckpt_040000.pth'
    )

    dist = util.infer_grid_sdfs(model=model,
                                grid_pts_tensor=grid_pts_tensor,
                                resolution=resolution)

    visible_pts = util.rendering_single_obj(
        resolution=resolution,
        sdf_length=sdf_length,
        step=step,
        viewpoint=viewpoint,
        obj_center=obj_center,
        angles=angles,
        grid_sdfs=dist.detach().cpu(),
        obj_boundary_sdf_threshold=obj_boundary_sdf_threshold)
    print(len(visible_pts))
    np.savetxt('scripts/results/tmp.txt', visible_pts)


def test_multi_obj_ray_cube():
    """测试多物体点云渲染
    渲染方案: 依据cube邻域射线采样点正负性确定渲染点
    """
    RESOLUTION = 128
    STEP = 0.01  # ray采样间隔
    VIEWPOINT = [0, 0, 0]
    OBJ_BOUNDARY_SDF_THRESHOLD = 0.01
    SCENE_CENTER = [24, 0, 6]
    NUM_SCAN_RAY = 50000
    FOV = 70.4

    # 加载素材
    obj_list = [
        # PCDMaterial(
        #     grid_sdf_path='SDF_models/NeuralPull/grid_sdf/shapnet/car.pt',
        #     obj_center=[27.669365, 4.7975054, 6.845917],
        #     obj_angles=[0, 0, 0],
        #     obj_size=3,
        #     label=cls2label['diaoWu']),
        DiaoBiMaterial(grid_sdf_path='SDF_models/grid_sdfs/diaobi/diaobi.pt',
                       obj_center=[24, 3.6, 0],
                       obj_size=40,
                       obj_angles=[0, 0, np.pi / 2],
                       label=cls2label['diaoBi']),
        DiaoBiMaterial(grid_sdf_path='SDF_models/grid_sdfs/diaobi/diaobi.pt',
                       obj_center=[24, 3.6, 0],
                       obj_size=30,
                       obj_angles=[0, 0, np.pi / 2],
                       label=cls2label['diaoBi'])
    ]

    # print(obj_list[0].get_arm_top_coord())

    visible_pts = util.rendering_multi_obj(
        resolution=RESOLUTION,
        step=STEP,
        viewpoint=VIEWPOINT,
        scene_center=SCENE_CENTER,
        obj_list=obj_list,
        obj_boundary_sdf_threshold=OBJ_BOUNDARY_SDF_THRESHOLD,
        num_scan_ray=NUM_SCAN_RAY,
        fov=FOV)
    print(f"rendering {len(visible_pts)} pts")
    np.savetxt('scripts/results/multi_obj.txt', visible_pts)


def test_ray_cube_intersection():
    ray_origin = np.array([0, 0, 0])  # 射线的起点
    ray_direction = np.array([1, 1, 1]) - ray_origin
    ray_direction = ray_direction / np.linalg.norm(ray_direction)

    box_min = np.array([-2, 2, -2])  # 立方体的最小边界
    box_max = np.array([2, 2, 2])  # 立方体的最大边界

    # 调用函数计算射线与立方体的交点
    intersection_point = util.ray_cube_intersection(ray_origin, ray_direction,
                                                    box_min, box_max)
    print(intersection_point)


def test_cal_grid_sdfs():
    util.cal_grid_sdfs(
        '/home/<USER>/workspace/3D/NeuralPull-Pytorch/outs/car/checkpoints/ckpt_040000.pth',
        save_path='SDF_models/NeuralPull/grid_sdf/shapnet/car.pt')


def test_rendering_scene():
    """实测渲染整个场景
    以吊臂为起点,找到吊臂顶点
    顶点与地面垂线方向随机出吊绳与吊物
    渲染人与牵引绳
    渲染杂物
    """
    VIEWPOINT = [0, 0, 0]
    RESOLUTION = 128
    STEP = 0.01
    NUM_SCAN = 200000
    FOV = 70.4
    SCENE_CENTER = [24, 0, 6]
    OBJ_BOUNDARY_SDF_THRESHOLD = 0.00

    diaobi_obj = DiaoBiMaterial(
        grid_sdf_path='SDF_models/diaobi.pt',
        obj_center=[24, 3.6, 0],
        obj_angles=[0, 0, np.pi / 2],
        obj_size=40,
        label=cls2label['diaoBi'])
    t1 = time()
    arm_top_coord = diaobi_obj.get_arm_top_coord()
    t2 = time()
    print(f"get_arm_top_coord:{t2-t1}")
    FORKED_SLING_COORD = deepcopy(arm_top_coord)  # 吊绳分叉起点
    FORKED_SLING_COORD[2] -= 2
    num_lines = 3
    diaowu_center = deepcopy(FORKED_SLING_COORD)  # 吊绳分叉起点
    diaowu_center[2] -= 4
    diaowu_obj = PCDMaterial(
        grid_sdf_path='SDF_models/car.pt',
        obj_center=diaowu_center,
        obj_angles=[0, 0, np.pi / 2],
        obj_size=4,
        label=cls2label['diaoWu'])
    # print(FORKED_SLING_COORD)
    # 渲染分叉吊绳
    diaowu_hanging_pts = util.get_link_point(viewpoint=FORKED_SLING_COORD,
                                             num_lines=num_lines,
                                             obj=diaowu_obj,
                                             resolution=RESOLUTION)
    t3 = time()
    print(f'get_link_point:{t3-t2}')
    if diaowu_hanging_pts is None:
        print('吊物挂点渲染失败')
        return
    forked_sling_list = [
        util.rendering_line(arm_top_coord, FORKED_SLING_COORD, noise_std=0.05)
    ]  # 分叉吊绳
    for pt in diaowu_hanging_pts:
        forked_sling_list.append(
            util.rendering_line(FORKED_SLING_COORD, pt, noise_std=0.05))

    forked_sling_pts = np.concatenate(forked_sling_list, 0)
    forked_sling_pts = np.concatenate([
        forked_sling_pts,
        (np.ones(
            (len(forked_sling_pts))) * cls2label['diaoShen']).reshape(-1, 1)
    ], 1)

    # 渲染吊物, 人, 牵引绳
    person_obj_list = [
        PCDMaterial(grid_sdf_path=
                    'SDF_models/person.pt',
                    obj_center=[13, 3, 0],
                    obj_angles=[-np.pi / 2, 0, 0],
                    obj_size=1.9,
                    label=cls2label['person']),
        PCDMaterial(grid_sdf_path=
                    'SDF_models/person.pt',
                    obj_center=[14, -3, 0],
                    obj_angles=[-np.pi / 2, 0, 0],
                    obj_size=2,
                    label=cls2label['person'])
    ]
    bg_obj_list = [
        PCDMaterial(
            grid_sdf_path='SDF_models/car.pt',
            obj_center=[20, 9, 0],
            obj_angles=[-np.pi / 2, 0, 0],
            obj_size=20,
            label=cls2label['bg'])
    ]
    scene_obj_list = person_obj_list + [diaowu_obj] + bg_obj_list
    # scene_obj_list = [diaobi_obj]
    # scene_pts = util.rendering_multi_obj(
    #     resolution=RESOLUTION,
    #     step=STEP,
    #     viewpoint=VIEWPOINT,
    #     scene_center=SCENE_CENTER,
    #     obj_list=scene_obj_list,
    #     obj_boundary_sdf_threshold=OBJ_BOUNDARY_SDF_THRESHOLD,
    #     fov=FOV,
    #     num_scan_ray=NUM_SCAN)
    # t4 = time()
    # scene_pts = np.concatenate([scene_pts, forked_sling_pts], 0)
    # print(f'rendering {len(scene_pts)} pts')

    lidar = Lidar(1.51, 26.375 / 180 * np.pi, 7294 / 60 * 2 * np.pi, -4664 / 60 * 2 * np.pi)
    scene = Scene([
       GridObject(
        torch.permute(obj.grid_sdf_matrix.reshape(
            *obj.grid_sdf_matrix.shape[-3:]
        ), [2, 1, 0]),
        obj.obj_size,
        obj.obj_center,
        obj.obj_angles
       ) for obj in scene_obj_list
    ])

    scene_pts = lidar.synthethis(scene, 0, 1, 10000, 0.01)
    
    np.savetxt('scripts/results/scene.txt', scene_pts[:, :3])


if __name__ == "__main__":
    # test_single_shape_rendering()
    # test_get_liftingpts()
    # rendering_whole_shape()
    # test_multi_shape_rendering()
    # test_gen_plane_grid()
    # test_plot_polar_rose()
    # test_sample_points()
    # test_ray_cube_visible_pts()
    # test_ray_cube_intersection()
    # test_cal_grid_sdfs()
    # test_multi_obj_ray_cube()
    test_rendering_scene()
