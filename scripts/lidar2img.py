import cv2
import numpy as np


def lidar2RangeImg(pcd, scale=0.31428571428571433, fov=70.4):
    nan_value = -1
    eps = 1e-10
    # calculate the resolution of range image
    scale = np.deg2rad(scale)
    fov = np.deg2rad(fov)
    w = h = int(np.ceil(fov / scale))

    colors_matrix = np.array([
        [255, 0, 0],  # 红色
        [0, 255, 0],  # 绿色
        [0, 0, 255],  # 蓝色
        [255, 255, 0],  # 黄色
        [128, 0, 128],  # 紫色
        [0, 255, 255]  # 青色
    ])

    range_img = np.full([w, h], nan_value, dtype=np.float32)
    vis_mask = np.full([w, h, 3], 0, dtype=np.uint8)
    range_idx = np.full([w, h], nan_value, dtype=np.int32)

    for idx, point in enumerate(pcd):
        x, y, z, label = point
        r = np.sqrt(x * x + y * y + z * z) + eps
        # [0, pi] -> [-pi/2, pi/2]
        # theta = np.arccos(z / r) - (np.pi / 2)   # theta 俯仰面
        theta = np.arcsin(-y / r)  # theta 俯仰面
        # [-pi, pi]
        # phi = np.arctan2(y, x + eps)  # phi 方位面
        phi = np.arctan2(-x, z + eps)

        # u = int((theta + fov / 2) / scale)
        # v = int((phi + fov / 2) / scale)

        u = int((fov / 2 - theta) / scale)
        # u = int((- theta) / scale)  # 错误示范
        v = int((fov / 2 - phi) / scale)
        # v = int((- phi) / scale)  # 错误示范
        # 1. grid is not assigned  2. current depth < range_img[u][v]

        if u > h or v > w or u < 0 or v < 0: continue
        if range_img[u][v] == nan_value:
            # range_img[u][v] = label*40  # 这里乘10是为了画图时候显示的更清楚
            vis_mask[u][v] = colors_matrix[int(label)]
            range_idx[u][v] = idx

    return vis_mask, range_idx


pcd = np.loadtxt('results/pcd/scene0_lidar0.txt')[:, :4]
range_img, idx = lidar2RangeImg(pcd)
print(range_img.shape)
cv2.imwrite('lidar2range.png', range_img)
