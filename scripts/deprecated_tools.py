import numpy as np
import torch
from tqdm import tqdm
from copy import deepcopy

from SDF_models.IGR.model import ImplicitNet
from . import util


def get_single_shape_visible_pcd(max_marching_steps,
                                 viewpoint,
                                 min_bound,
                                 max_bound,
                                 resolution,
                                 SDF_mdoel,
                                 scale=1.0,
                                 R=None):
    '''
    计算单个图形的可视点(deprecated) only for debug
    max_marching_steps: ray marching迭代最大次数
    viewpoint
    min_bound: 网格最小边界 [min_x,min_y,min_z]
    max_bound: 
    '''
    grid_points = util.get_grid_uniform(min_bound, max_bound, resolution)
    num_pts = len(grid_points)
    batch_vps = np.tile(np.array(viewpoint).reshape(1, 3), (num_pts, 1))
    ray_directions = grid_points - batch_vps
    ray_directions /= np.linalg.norm(ray_directions, axis=1)[:, np.newaxis]
    ray_directions = torch.tensor(ray_directions, dtype=torch.float32).cuda()
    cur_pts = torch.tensor(batch_vps, dtype=torch.float32).cuda()
    num_split = 2000000
    cur_pts_list = list(torch.split(cur_pts, num_split, dim=0))
    ray_dir_list = list(torch.split(ray_directions, num_split, dim=0))
    EPSILON = 1e-3
    last_dist_list = []
    with torch.no_grad():
        for step in tqdm(range(max_marching_steps)):
            for idx, cur_pt, ray_dir in zip(range(len(cur_pts_list)),
                                            cur_pts_list, ray_dir_list):
                # if R is not None:

                dists = SDF_mdoel(cur_pt / scale) * scale
                cur_pt = cur_pt + dists * ray_dir
                cur_pts_list[idx] = cur_pt
                if step == max_marching_steps - 1:
                    last_dist_list.append(dists.detach().cpu().numpy())
    end_pts = torch.cat(cur_pts_list, 0).detach().cpu().numpy()
    las_dist = np.concatenate(last_dist_list, 0).reshape(-1)
    intersection_mask = las_dist < EPSILON
    intersection_pts = end_pts[intersection_mask]
    print(f"rendering {len(intersection_pts)} points")
    # np.savetxt('scripts/results/single.txt', intersection_pts)
    # np.savetxt('scrips/results/grid_points.txt', grid_points)
    return intersection_pts


def get_multi_shape_visible_pcd(max_marching_steps,
                                viewpoint,
                                min_bound,
                                max_bound,
                                resolution,
                                SDF_mdoel_list,
                                shape_shift_list=[[0, 0, 0]],
                                shape_R_list=[],
                                shape_scale_list=[],
                                num_pt=100000):
    """ray marching rendering method(deprecated)

    Args:
        max_marching_steps (_type_): _description_
        viewpoint (_type_): _description_
        min_bound (_type_): _description_
        max_bound (_type_): _description_
        resolution (_type_): _description_
        SDF_mdoel_list (_type_): _description_
        shape_shift_list (list, optional): _description_. Defaults to [[0, 0, 0]].
        shape_R_list (list, optional): _description_. Defaults to [].
        shape_scale_list (list, optional): _description_. Defaults to [].
        num_pt (int, optional): _description_. Defaults to 100000.

    Returns:
        _type_: _description_
    """
    assert len(SDF_mdoel_list) == len(shape_R_list) == len(shape_scale_list)
    # grid_points = get_grid_uniform(min_bound, max_bound, resolution)
    min_bound = np.asarray(min_bound)
    max_bound = np.asarray(max_bound)
    center = (np.asarray(min_bound) + np.asarray(max_bound)) / 2
    size = np.max(max_bound - min_bound) * 1.5
    # grid_points = get_plane_grid(viewpoint=viewpoint,
    #                              plane_center=center,
    #                              size=size,
    #                              resulotion=resolution)
    # grid_points = get_polar_rose_grid(viewpoint=viewpoint,
    #                                   plane_center=center,
    #                                   r=size / 2,
    #                                   num_pt=num_pt)
    grid_points = np.asarray([[0, 0, 0]]).astype(np.float32)
    num_pts = len(grid_points)
    batch_vps = np.tile(np.array(viewpoint).reshape(1, 3),
                        (num_pts, 1)).astype(np.float32)
    ray_directions = grid_points - batch_vps
    ray_directions /= np.linalg.norm(ray_directions, axis=1)[:, np.newaxis]
    ray_directions = torch.tensor(ray_directions, dtype=torch.float32).cuda()
    cur_pts = torch.tensor(batch_vps, dtype=torch.float32).cuda()
    num_split = 100000
    cur_pts_list = list(torch.split(cur_pts, num_split, dim=0))
    ray_dir_list = list(torch.split(ray_directions, num_split, dim=0))
    EPSILON = 1e-2
    last_dist_list = []

    with torch.no_grad():
        for step in tqdm(range(max_marching_steps)):
            for idx, cur_pt, ray_dir in zip(range(len(cur_pts_list)),
                                            cur_pts_list, ray_dir_list):
                shape_dist_list = []
                for model, shift, R, scale_factor in zip(
                        SDF_mdoel_list, shape_shift_list, shape_R_list,
                        shape_scale_list):
                    trans_pts = util.shift_pts(shift, cur_pt)
                    # trans_pts, _ = rotate_pt(ptA=torch.tensor([0.0, 0.0,
                    #                                            0.0]).cuda(),
                    #                          ptsB=trans_pts,
                    #                          angles_XYZ=angles)
                    trans_pts = util.rotate_pts(ptA=torch.tensor(
                        [0.0, 0.0, 0.0]).cuda(),
                                                ptsB=trans_pts,
                                                R=R)
                    shape_dist_list.append(
                        model(trans_pts / scale_factor) * scale_factor)
                dists = torch.cat(shape_dist_list, 1)

                print(torch.min(dists))
                min_dist = torch.min(dists, 1)[0].reshape(-1, 1)
                cur_pt = cur_pt + min_dist * ray_dir
                cur_pts_list[idx] = cur_pt
                if step == max_marching_steps - 1:
                    last_dist_list.append(min_dist.detach().cpu().numpy())
    end_pts = torch.cat(cur_pts_list, 0).detach().cpu().numpy()
    las_dist = np.concatenate(last_dist_list, 0).reshape(-1)
    intersection_mask = las_dist < EPSILON
    intersection_pts = end_pts[intersection_mask]

    # np.savetxt('scripts/results/grid_points.txt', grid_points)
    return intersection_pts


def get_diaoWu_liftingPoint(diaoWuCenter_pt,
                            apexJib_pt,
                            SDF_model_diaowu,
                            num_lines,
                            min_bound=np.asarray([-1, -1, -1]),
                            max_bound=np.asarray([1, 1, 1]),
                            R=None):
    '''
    计算吊物吊点(deprecated)
    Args:
        diaoWuCenter_pt: 吊物中心点坐标
        apexJib_pt: 吊臂顶点坐标
        SDF_model_diaowu: 吊物SDF模型
    '''
    if R is None:
        R = util.get_rotate_matrix([0, 0, 0])
    viewpoint = apexJib_pt - diaoWuCenter_pt
    pts = get_multi_shape_visible_pcd(max_marching_steps=20,
                                      viewpoint=viewpoint,
                                      min_bound=min_bound,
                                      max_bound=max_bound,
                                      resolution=512,
                                      SDF_mdoel_list=[SDF_model_diaowu],
                                      shape_shift_list=[[0, 0, 0]],
                                      shape_R_list=[R],
                                      shape_scale_list=[1])
    np.savetxt('scripts/results/tmp.txt', pts)
    if len(pts) < num_lines:
        print('can not find enough lifting pts')
        return None
    liftingPts = util.farthest_point_sample(pts, num_lines)

    return liftingPts + diaoWuCenter_pt


def load_IGR_model(ckpt_path):
    # ckpt_path = 'exps/single_shape/2023_07_11_22_12_39/checkpoints/ModelParameters/100000.pth'
    model = ImplicitNet(d_in=3,
                        dims=[512, 512, 512, 512, 512, 512, 512, 512],
                        skip_in=[4],
                        geometric_init=True,
                        radius_init=1,
                        beta=100)
    ckpt = torch.load(ckpt_path, map_location='cpu')
    model.load_state_dict(ckpt['model_state_dict'])
    model.eval()
    model = model.cuda()
    return model


def rendering_scene_by_rayMarching():
    '''
    deprecated method: sdf distance is not stable
    渲染整个场景,吊臂在吊东西,周边乱七八糟的杂物,人拉着牵引绳
    '''
    x_range = np.arange(0, 6)
    y_range = np.arange(-3, 3)
    z_range = np.arange(2, 5)

    VIEWPOINT = [5, 5, 0]
    MIN_BOUND = np.asarray([-1, -4, 0])
    MAX_BOUND = np.asarray([7, 4, 6])
    RESOLUTION = 256
    NUM_POLAR_ROSE_PT = 100000

    # 渲染地面点
    ground_pts = util.random_ground_pts(x_range, y_range, 5000)

    SDF_ckpt_paths = util.get_NeuralPull_ckpt_paths()
    # 渲染地面杂物
    bg_ckpt_list = np.random.choice(SDF_ckpt_paths, 5)
    num_bg = len(bg_ckpt_list)
    model_list = [util.load_NeuralPull_model(path) for path in bg_ckpt_list]
    pos_list = [
        util.random_pos(x_range, y_range, np.arange(0.5, 1))
        for i in range(num_bg)
    ]
    shape_rotate_list = []
    shape_scale_list = []
    for _ in range(len(pos_list)):
        angles = np.arange(0, 2 * np.pi, 360)
        sample_angles = np.random.choice(angles, 3)
        R = util.get_rotate_matrix(angles_XYZ=sample_angles)
        scale = np.random.choice(np.arange(2, 5), 1)[0]
        shape_rotate_list.append(R)
        shape_scale_list.append(scale)

    # diaowu
    diaowu_ckpt_path = np.random.choice(SDF_ckpt_paths, 1)[0]
    SDF_diaowu = util.load_NeuralPull_model(diaowu_ckpt_path)
    diaowu_center_pos = util.random_pos(x_range, y_range, z_range)
    diaowu_angles = np.random.choice(np.arange(0, 2 * np.pi), 3)
    diaowu_R = util.get_rotate_matrix(diaowu_angles)
    diaowu_scale = np.random.choice(np.arange(2, 4), 1)[0]

    model_list.append(SDF_diaowu)
    pos_list.append(diaowu_center_pos)
    shape_rotate_list.append(diaowu_R)
    shape_scale_list.append(diaowu_scale)

    # cal lifting points
    apexJib_pt = deepcopy(diaowu_center_pos)
    apexJib_pt[2] += 3
    lifting_pts = get_diaoWu_liftingPoint(
        diaowu_center_pos,
        apexJib_pt,
        SDF_diaowu,
        4,
    )
    if lifting_pts is None:
        raise Exception('lifting_pts is None')
    # rendering lines
    lines = []
    for dst_pt in lifting_pts:
        lines.append(
            util.rendering_line(start_pt=apexJib_pt,
                                end_pt=dst_pt,
                                num_points=100,
                                noise_std=0))
    lines_pts = np.concatenate(lines, 0)

    pts = get_multi_shape_visible_pcd(max_marching_steps=20,
                                      viewpoint=VIEWPOINT,
                                      min_bound=MIN_BOUND,
                                      max_bound=MAX_BOUND,
                                      resolution=RESOLUTION,
                                      SDF_mdoel_list=model_list,
                                      shape_shift_list=pos_list,
                                      shape_R_list=shape_rotate_list,
                                      shape_scale_list=shape_scale_list,
                                      num_pt=NUM_POLAR_ROSE_PT)
    print(f"rendering {len(pts)} points")

    pts = np.concatenate([pts, ground_pts, lines_pts], 0)

    # 滤除地面以下点 添加噪声
    pts = pts[pts[:, 2] >= 0]
    noise = np.random.normal(0, 0.02, size=pts.shape)
    pts += noise
    np.savetxt('scripts/results/tmp.txt', pts)


def random_ground_pts(x_range, y_range, num_pts):
    x = np.random.choice(np.arange(x_range[0], x_range[-1], step=0.1), num_pts)
    y = np.random.choice(np.arange(y_range[0], y_range[-1], step=0.1), num_pts)
    z = np.zeros(((num_pts)))
    pts = np.vstack([x, y, z]).T
    return pts