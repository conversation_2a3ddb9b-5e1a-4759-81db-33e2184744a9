import numpy as np


class DoublePrism:
    r"""
        旋转双棱镜扫描方式

        Arguments:
        ng(float): 棱镜的折射率
        alpha(float): 棱镜的倾斜角
        omega1(float): 第一个棱镜的旋转角速度
        omega2(float): 第二个棱镜的旋转角速度
    """

    def __init__(self, ng, alpha, omega1, omega2):
        self.ng = ng
        self.alpha = alpha
        self.omega1 = omega1
        self.omega2 = omega2

        self.t = 0

    def clear(self):
        self.t = 0

    @property
    def inter_ray(self):
        # 返回当前入射光
        theta1 = self.theta1
        alpha = self.alpha - np.arcsin(np.sin(self.alpha) / self.ng)

        return np.array([0, 0, np.cos(alpha)], dtype = np.float32) + \
            np.array([np.cos(theta1), np.sin(theta1), 0], dtype = np.float32) * np.sin(alpha)

    @property
    def normal(self):
        # 返回当前的法线方向
        theta2 = self.theta2 + np.pi

        return np.array([0, 0, np.cos(self.alpha)], dtype = np.float32) + \
            np.array([np.cos(theta2), np.sin(theta2), 0], dtype = np.float32) * np.sin(self.alpha)

    @property
    def theta1(self):
        return np.pi / 2 + self.t * self.omega1

    @property
    def theta2(self):
        return np.pi / 2 + self.t * self.omega2

    def step(self, time_interval):

        inter_ray = self.inter_ray
        normal = self.normal

        # 计算输入光线在法线方向上的垂直分量
        inter_v = inter_ray - inter_ray.dot(normal) * normal

        # 根据折射定律，出射光线的垂直分量会按照折射率扩大一个倍数
        outer_v = self.ng * inter_v

        # 将棱镜旋转一个角度
        self.t += time_interval

        if np.linalg.norm(outer_v) >= 1:
            return np.zeros(3, dtype=np.float32)

        return outer_v + np.sqrt(1 - np.linalg.norm(outer_v)**2) * normal

    def integrate(self, time_start, T, N):
        r"""
            点云积分

            Arguments:
            time_start(float): 积分开始时间
            T(float): 积分时间
            N(int): 积分点云的个数
        """

        times = np.linspace(0, T, N) + time_start

        thetas1 = self.omega1 * times + np.pi / 2
        thetas2 = self.omega2 * times + np.pi / 2 + np.pi

        return compute_outer_ray(self.ng, self.alpha, thetas1, thetas2)


def compute_outer_ray(ng, alpha, thetas1, thetas2):
    r"""
        根据旋转双棱镜的参数配置计算出射光的方向
    """

    # 计算入射光的方向
    theta_p = alpha - np.arcsin(np.sin(alpha) / ng)

    inter_z = np.array([0, 0, np.cos(theta_p)], dtype=np.float32)
    inter_xy = np.sin(theta_p) * np.concatenate([
        np.cos(thetas1).reshape(-1, 1),
        np.sin(thetas1).reshape(-1, 1),
        np.zeros_like(thetas1).reshape(-1, 1)
    ],
                                                axis=1)
    inter_ray = inter_z + inter_xy

    # 计算法线方向
    normal_z = np.array([0, 0, np.cos(alpha)])
    normal_xy = np.sin(alpha) * np.concatenate([
        np.cos(thetas2).reshape(-1, 1),
        np.sin(thetas2).reshape(-1, 1),
        np.zeros_like(thetas2).reshape(-1, 1)
    ],
                                               axis=1)
    normal = normal_z + normal_xy

    # 计算入射光在法线垂直方向上的投影
    inter_v = inter_ray - np.sum(inter_ray * normal, axis=1,
                                 keepdims=True) * normal
    outer_v = inter_v * ng

    outer_v_norm = np.linalg.norm(outer_v, axis=1)
    avail_indices = (outer_v_norm <= 1)

    outer = np.zeros_like(inter_ray)

    outer[avail_indices] = outer_v[avail_indices] + np.sqrt(
        1 - outer_v_norm[avail_indices]**2).reshape(-1,
                                                    1) * normal[avail_indices]

    return outer


if __name__ == '__main__':
    import matplotlib.pyplot as plt
    from matplotlib.animation import FuncAnimation

    prism = DoublePrism(1.51, 26.375 / 180. * np.pi, 7294 / 60 * 2 * np.pi,
                        -4664 / 60 * 2 * np.pi)
    T = 1
    N = int(T * 100000)
    prism.clear()

    outer_ray = prism.integrate(0, T, N)

    figure = plt.figure()
    ax = figure.add_subplot()

    ax.set_xlim(-40, 40)
    ax.set_ylim(-40, 40)

    x = np.arctan2(outer_ray[:, 0], outer_ray[:, 2]) / np.pi * 180
    y = np.arctan2(outer_ray[:, 1], outer_ray[:, 2]) / np.pi * 180

    xs = []
    ys = []

    for i in range(1):
        xs.append(x + i * 1)
        ys.append(y)

    x = np.array(xs)
    y = np.array(ys)

    line = ax.plot([], [], marker='.', ls='', markersize=1)[0]

    # plt.show()


    def update(frame):

        # line.set_data(-y[:frame], x[:frame])

        line.set_data(-y[:, :frame].flatten(), x[:, :frame].flatten())

        return line

    ani = FuncAnimation(figure, update, N, interval=1)

    plt.show()
