from typing import List

import numpy as np
import torch
from tqdm import tqdm
from scipy.spatial import cKDTree
from time import time
import random
import sdf
from copy import deepcopy

import scene._utils as _utils
from scripts import util

GRID_OBJECT_ID = 0
cls2label = {
    'bg': 0,
    'diaoWu': 1,
    "diaoBi": 2,
    'person': 3,
    'diaoSheng': 4,
    'qianYinSheng': 5
}
label2cls = {cls2label[key]: key for key in cls2label.keys()}


class GridObject:

    def __init__(self, grid_sdf_path, t, angles, obj_scale=2.0, label=-1):
        """grid object

        Args:
            grid_sdf_path (str): grid sdf data path
            obj_scale (float, optional): 物体大小缩放系数. Defaults to 2.0.
            t (_type_): obj translation
            angles (_type_): obj rotated angles [-np.pi,np.pi]
            label (int, optional): obj label. Defaults to -1.
        """

        data = torch.load(grid_sdf_path, map_location='cpu')
        self.data = data
        if isinstance(data, dict):
            grid_sdf_matrix = torch.tensor(data['grid_tensor'],
                                           dtype=torch.float32)
            self.grid = grid_sdf_matrix.reshape(
                grid_sdf_matrix.shape[-3:]).permute([2, 1, 0])
            xyz_size = np.array(data['max_bound'] - data['min_bound'],
                                dtype=np.float32)
        else:
            self.grid = data
            xyz_size = np.array([2, 2, 2], dtype=np.float32)
        self.size = xyz_size * obj_scale
        self.grid_path = grid_sdf_path
        self.label = label
        self.obj_scale = obj_scale

        self.obj2scene = _utils.get_transform_mat(np.array(t), angles)

        global GRID_OBJECT_ID
        self.ID = GRID_OBJECT_ID
        GRID_OBJECT_ID += 1

        # self.step = min(self.size) / 128 * 0.3

    def __str__(self):
        return f"ID:{self.ID}, size:{self.size}, label: {label2cls[self.label]},t:{self.obj2scene[:,3]}"

    def moveto(self, t):
        self.obj2scene[:, 3] = t

    def rotate(self, axis, angles):
        R = self.obj2scene[:, :3]

        _R = _utils.get_rotation_mat(axis, angles)

        self.obj2scene[:, :3] = R @ _R

    def scale(self, scale):
        self.size = self.size * float(scale)

    @torch.no_grad()
    def __call__(self, points):
        r"""
            GridSample 在传入坐标时，按变化由快到慢的维度顺序传入

            Arguments:
            points: 是物体坐标系下表示的点的位置
        """
        # 首先将物体坐标系的原点移动到grid的左上角
        points = points + self.size * 0.5

        # 然后将点的坐标转换到sample坐标
        points = points / self.size * 2 - 1

        points = torch.tensor(points).reshape(1, -1, 1, 1, 3).float()
        grid = self.grid.reshape(1, 1, *self.grid.shape)

        sdfs = torch.nn.functional.grid_sample(grid,
                                               points,
                                               mode='bilinear',
                                               align_corners=True)

        return sdfs.numpy().flatten()

    def intersection_with_bbox(self, ray_o, ray_d):
        r"""
            计算光线与立方体之间的交点
        """
        p1, p2 = None, None

        # z平面
        z = -self.size[2] / 2
        if ray_d[2] != 0:
            lamb = (z - ray_o[2]) / ray_d[2]
            point = ray_o + lamb * ray_d

            if np.abs(point[0]) <= self.size[0] / 2 and np.abs(
                    point[1]) <= self.size[1] / 2:
                p1 = point

        z = self.size[2] / 2
        if ray_d[2] != 0:
            lamb = (z - ray_o[2]) / ray_d[2]
            point = ray_o + lamb * ray_d

            if np.abs(point[0]) <= self.size[0] / 2 and np.abs(
                    point[1]) <= self.size[1] / 2:
                if p1 is not None: p2 = point
                else: p1 = point

        # x平面
        x = -self.size[0] / 2
        if ray_d[0] != 0:
            lamb = (x - ray_o[0]) / ray_d[0]
            point = ray_o + lamb * ray_d

            if np.abs(point[1]) <= self.size[1] / 2 and np.abs(
                    point[2]) <= self.size[2] / 2:
                if p1 is not None: p2 = point
                else: p1 = point

        x = self.size[0] / 2
        if ray_d[0] != 0:
            lamb = (x - ray_o[0]) / ray_d[0]
            point = ray_o + lamb * ray_d

            if np.abs(point[1]) <= self.size[1] / 2 and np.abs(
                    point[2]) <= self.size[2] / 2:
                if p1 is not None: p2 = point
                else: p1 = point

        # y平面
        y = -self.size[1] / 2
        if ray_d[1] != 0:
            lamb = (y - ray_o[1]) / ray_d[1]
            point = ray_o + lamb * ray_d

            if np.abs(point[0]) <= self.size[0] / 2 and np.abs(
                    point[2]) <= self.size[2] / 2:
                if p1 is not None: p2 = point
                else: p1 = point

        y = self.size[1] / 2
        if ray_d[1] != 0:
            lamb = (y - ray_o[1]) / ray_d[1]
            point = ray_o + lamb * ray_d

            if np.abs(point[0]) <= self.size[0] / 2 and np.abs(
                    point[2]) <= self.size[2] / 2:
                if p1 is not None: p2 = point
                else: p1 = point

        return p1, p2

    def sample_points(self, ray_o, ray_d, step):
        r"""
            沿光线方向进行采样

            Arguments:
            ray_o: 在场景世界坐标系中表示的光线原点
            ray_d: 在场景世界坐标系中表示的光线方向
            step: 采样间隔
        """

        # 首先将光线转换到物体坐标系下
        R = self.obj2scene[:, :3]
        t = self.obj2scene[:, 3]

        ray_o = R.T.dot(ray_o) - R.T.dot(t)
        ray_d = R.T.dot(ray_d)

        # 计算光线与物体bbox的入射/出射点
        p1, p2 = self.intersection_with_bbox(ray_o, ray_d)

        if p1 is None or p2 is None:
            return []

        # 在p1, p2之间，以步长step进行采样
        d1 = np.linalg.norm(p1 - ray_o)
        d2 = np.linalg.norm(p2 - ray_o)
        if d1 > d2:
            d1, d2 = d2, d1

        return ray_o + ray_d * np.arange(d1, d2 + step, step).reshape(-1, 1)

    def distance(self, ray_o, ray_d, step):
        r"""
            计算光线与物体的交点距光线开端处的距离
            
            Arguments:
            ray_o: 场景世界坐标系下表示的光线原点
            ray_d: 场景世界坐标系下表示的光线发射方向
            step: 采样间隔
        """

        # 沿光线进行采样
        # 注意： 此时返回的点的坐标在物体坐标系下
        points = self.sample_points(ray_o, ray_d, step)

        if len(points) < 2:
            # 光线与物体的bbox不相交，没有采样点，直接返回
            # print('光线与物体的bbox不相交')
            return 9999
        
        # 计算采样点处的SDF值
        sdfs = self(points)

        # 计算交点所在的位置
        indices = np.arange(len(sdfs))[sdfs < 0.01]

        if len(indices) <= 0:
            # 所有的sdf值均大于0, 光线与物体不相交
            # print('SDF值均大于零')
            return 9999
        
        # 寻找SDF值首次变为负数的位置
        idx = np.min(indices)

        if idx == 0:
            # 异常：光线原点在物体内部
            # print('光线原点在物体内部')
            return 9999

        p1 = points[idx - 1]
        d1 = sdfs[idx - 1]
        p2 = points[idx]
        d2 = np.abs(sdfs[idx])

        point = p1 * d2 / (d1 + d2) + p2 * d1 / (d1 + d2)

        # 再将计算得到的点转换回场景世界坐标系
        R = self.obj2scene[:, :3]
        t = self.obj2scene[:, 3]
        point = R.dot(point) + t

        return np.linalg.norm(point - ray_o)

    def sample_neg_grid_pts(self):
        # 在物体坐标系采样物体内部点
        sx = np.arange(-self.size[0] / 2, self.size[0] / 2, self.size[0] / 128)
        sy = np.arange(-self.size[1] / 2, self.size[1] / 2, self.size[1] / 128)
        sz = np.arange(-self.size[2] / 2, self.size[2] / 2, self.size[2] / 128)
        xx, yy, zz = np.meshgrid(sx, sy, sz, indexing='ij')
        grid_points_mnp = np.stack([xx, yy, zz], -1)  # MNP
        sample_pts = grid_points_mnp.reshape(-1, 3)
        grid_sdf = self.grid.permute((2, 1, 0)).numpy().reshape((-1, 1))
        mask = (grid_sdf < 0.1)[:, 0]
        sample_pts = sample_pts[mask]

        sample_pts = np.concatenate(
            [sample_pts, np.ones((sample_pts.shape[0], 1))], 1)

        # 转至世界坐标系
        sample_pts = sample_pts.dot(self.obj2scene.T)
        return sample_pts


class DiaoBi(GridObject):
    """吊臂物体
        相比于GridObject,吊臂拥有diaobi_upper(吊臂顶点坐标)特有属性
    """

    def __init__(self, grid_sdf_path, t, angles, obj_scale=2, label=-1):
        super().__init__(grid_sdf_path, t, angles, obj_scale, label)
        assert 'diaobi_upper' in self.data.keys(), "吊臂顶点在数据pt中缺失"
        self.diaobi_upper_ori = self.data['diaobi_upper']

    def update_diaobi_upper_pt(self):
        """
        更新吊臂顶点，返回场景坐标系下的吊臂顶点
        Args:
            diaobi_upper: np.array([x, y, z])  pt文件中的原始吊臂顶点
        Returns:
            diaobi_upper_scene: (ndarray) 场景坐标系中吊臂顶点坐标
        """
        assert self.label == cls2label['diaoBi']

        # obj coor  ->  scale
        diaobi_upper = self.diaobi_upper_ori.copy()
        diaobi_upper[:2] *= self.obj_scale
        diaobi_upper[2] = diaobi_upper[2] / (
            (self.size[2] / self.obj_scale) / 2) * (self.size[2] / 2)

        # scene coor
        R = self.obj2scene[:, :3]
        t = self.obj2scene[:, 3]
        diaobi_upper_scene = np.array([diaobi_upper]) @ R.T + np.array([t])

        return diaobi_upper_scene.flatten()


class ShengZi(GridObject):

    def __init__(self, grid_sdf_path, t, angles, obj_scale=2, label=-1):
        super().__init__(grid_sdf_path, t, angles, obj_scale, label)

        data = torch.load(grid_sdf_path, map_location='cpu')
        if isinstance(data, dict):
            grid_sdf_matrix = torch.tensor(data['grid_tensor'],
                                           dtype=torch.float32)
            self.grid = grid_sdf_matrix.reshape(
                grid_sdf_matrix.shape[-3:]).permute([2, 1, 0])
            xyz_size = np.array(data['max_bound'] - data['min_bound'],
                                dtype=np.float32)
        else:
            self.grid = data
            xyz_size = np.array([2, 2, 2], dtype=np.float32)
        # self.obj_scale = obj_scale
        self.size = xyz_size * obj_scale
        self.size[0] = xyz_size[0]
        self.size[-1] = xyz_size[-1]

    def scale(self, scale):
        raise NotImplementedError


class Scene:

    def __init__(self, models: List[GridObject]):
        self.models = models

    def synthethis(self, rays_o, rays_d, step):
        points = []
        for ray_o, ray_d in tqdm(zip(rays_o, rays_d)):
            distance = [
                model.distance(ray_o, ray_d, step) for model in self.models
            ]

            idx = np.argmin(distance)

            if distance[idx] <= 100:
                point = ray_o + distance[idx] * ray_d
                label = self.models[idx].label
                obj_ID = self.models[idx].ID

                points.append(point.tolist() + [label, obj_ID])

        return np.array(points)

    def update_diaobi_upper_scene(self):
        """
        如果是吊臂，则更新吊臂顶点，返回场景坐标系下的吊臂顶点坐标

        """
        diaobi_upper_pt_list = []
        for model in self.models:
            if model.label == cls2label['diaoBi']:
                diaobi_upper_pt_scene = model.update_diaobi_upper_pt()
                diaobi_upper_pt_list.append(diaobi_upper_pt_scene)

        return np.array(diaobi_upper_pt_list)

    @classmethod
    def cal_obj_min_distance(self, grid_obj1: GridObject,
                             grid_obj_list: List[GridObject]):
        """计算obj与obj list之间最短距离

        Args:
            grid_obj1 (_type_): _description_
            grid_obj_list (_type_): _description_

        Returns:
            _type_: grid_obj1到grid_obj_list之间最短距离
        """
        sample_pts1 = grid_obj1.sample_neg_grid_pts()
        sample_pts2 = np.concatenate(
            [obj.sample_neg_grid_pts() for obj in grid_obj_list], 0)
        # KD tree找出最近距离
        tree_1 = cKDTree(sample_pts1)
        closest_distances, _ = tree_1.query(sample_pts2, k=1)
        min_distance = np.min(closest_distances)
        return min_distance

    def move_obj_to_ground(
            self,
            pts,
            label_list=[cls2label['diaoBi'], cls2label['person']],
            ground_y=0):
        """将所有obj移动到地面上
        需同步进行：
        1. 更新所有物体中心坐标
        2. 更新吊臂顶点

        TODO: 支持吊臂与吊物绑定以支持多吊臂渲染
        Args:
            pts (_type_): 世界坐标系下点
            label_list: 所有需要移动到地面上的物体label
            ground_y: 最低面高度

        Returns:
            _type_: _description_
        """
        id2obj = {obj.ID: obj for obj in self.models}

        ids = pts[:, 4]
        id_set = set(ids)
        for id in id_set:
            mask = pts[:, 4] == id
            obj_label = pts[mask][0, 3]
            if obj_label not in label_list: continue
            max_y = np.max(pts[mask][:, 1])
            min_margin = ground_y - max_y
            pts[mask, 1] = pts[mask, 1] + min_margin
            id2obj[id].obj2scene[:, 3][1] += min_margin
            # 若移动吊臂,需同步移动吊物
            if obj_label == cls2label['diaoBi']:
                mask = pts[:, 3] == cls2label['diaoWu']
                pts[mask, 1] = pts[mask, 1] + min_margin
                for obj in self.models:
                    if obj.label != cls2label['diaoWu']: continue
                    obj.obj2scene[:, 3][1] += min_margin


class Object3D(GridObject):

    def __init__(self,
                 t,
                 angles,
                 xyz_size=[1, 2, 3],
                 label=-1,
                 sdf_func=sdf.box((1, 2, 3)),
                 R=_utils.get_rotation_mat_x(90.0 / 180.0 * np.pi)):
        """3D素材基类

        Args:
            t (list): 空间位置
            angles (list): 旋转角度
            xyz_size (list, optional): 物体绝对大小. Defaults to [1, 2, 3].
            label (int, optional): 物体类别. Defaults to -1.
            sdf_func (_type_, optional): sdf函数. Defaults to None.
        """
        self.size = np.asarray(xyz_size)
        self.label = label
        self.obj_scale = 1
        self.obj2scene = _utils.get_transform_mat(np.array(t), angles)

        global GRID_OBJECT_ID
        self.ID = GRID_OBJECT_ID
        GRID_OBJECT_ID += 1

        # 扩大grid计算的范围，否则可能会导致物体边界与grid边界重叠，
        # 这会干扰后续的表面交点计算，例如box这种不给grid任何边界的物体
        bd_min = -self.size / 2 - 0.5
        bd_max = self.size / 2 + 0.5

        grid = self.calculate_grid(bd_min=bd_min,
                                   bd_max=bd_max,
                                   sdf_func=sdf_func,
                                   R=R)
        grid_sdf_matrix = torch.tensor(grid, dtype=torch.float32)
        self.grid = grid_sdf_matrix.reshape(
            grid_sdf_matrix.shape[-3:]).permute([2, 1, 0])
        self.grid_path = 'None'

    def calculate_grid(self,
                       bd_min,
                       bd_max,
                       sdf_func,
                       resolution=128,
                       R=np.eye(3)):
        N = 8
        assert resolution % N == 0

        X = np.linspace(bd_min[0], bd_max[0], resolution)
        X = np.array_split(X, N)
        Y = np.linspace(bd_min[1], bd_max[1], resolution)
        Y = np.array_split(Y, N)
        Z = np.linspace(bd_min[2], bd_max[2], resolution)
        Z = np.array_split(Z, N)

        v = np.zeros([resolution, resolution, resolution], dtype=np.float32)

        for xi, xs in enumerate(X):
            for yi, ys in enumerate(Y):
                for zi, zs in enumerate(Z):
                    xx, yy, zz = np.meshgrid(xs, ys, zs, indexing='ij')
                    # xx, yy, zz = np.meshgrid(xs, ys, zs)
                    pts = np.concatenate([
                        xx.reshape(-1, 1),
                        yy.reshape(-1, 1),
                        zz.reshape(-1, 1)
                    ],
                                         axis=-1)
                    val = sdf_func(pts @ R.T).reshape(len(xs), len(ys),
                                                      len(zs))
                    v[xi * int(resolution / N):xi * int(resolution / N) +
                      len(xs), yi *
                      int(resolution / N):yi * int(resolution / N) + len(ys),
                      zi * int(resolution / N):zi * int(resolution / N) +
                      len(zs)] = val
        return v


class Object3D_diaoBi(Object3D):

    def __init__(self,
                 t,
                 angles,
                 xyz_size=[1, 2, 3],
                 label=-1,
                 sdf_func=sdf.box((1, 2, 3)),
                 R=_utils.get_rotation_mat_x(90 / 180 * np.pi)):
        super().__init__(t, angles, xyz_size, label, sdf_func, R)

    def update_diaobi_upper_pt(self):
        pts = self.sample_neg_grid_pts()
        indices = np.argsort(pts[:, 1])[:50]
        return np.mean(pts[indices], axis=0)


class ObjectFactory:

    @classmethod
    def get_rounded_box(self, t, angles=[0,0,0], xyz_size=[1, 2, 3], label=-1):
        sdf_func = sdf.box(xyz_size)
        return Object3D(t=t,
                        angles=angles,
                        xyz_size=np.asarray(xyz_size)+1.0,
                        label=label,
                        sdf_func=sdf_func,
                        R=np.eye(3))
    @classmethod
    def get_sphere(self, t, angles, r=1, label=-1):
        sdf_func = sdf.sphere(r)
        xyz_size = [2 * r, 2 * r, 2 * r]
        return Object3D(t=t,
                        angles=angles,
                        xyz_size=xyz_size,
                        label=label,
                        sdf_func=sdf_func,
                        R=np.eye(3))

    @classmethod
    def get_rounded_cylinder(self,
                             t,
                             angles=[0, 0, np.pi / 2],
                             ra=1,
                             rb=0.2,
                             h=3,
                             label=-1):
        """默认竖直状态圆柱

        Args:
            t (_type_): _description_
            angles (_type_): _description_
            ra (_type_): 圆柱半径
            rb (_type_): 控制圆柱圆润程度
            h (_type_): 圆柱高
            label (int, optional): _description_. Defaults to -1.

        Returns:
            _type_: _description_
        """
        sdf_func = sdf.rounded_cylinder(ra, rb, h)
        xyz_size = [2 * ra, h, 2 * ra]
        return Object3D_diaoBi(t=t,
                               angles=angles,
                               xyz_size=xyz_size,
                               label=label,
                               sdf_func=sdf_func)

    @classmethod
    def get_rounded_cone(self,
                         t,
                         angles=[0, np.pi / 4, np.pi / 4],
                         ra=0.8,
                         rb=0.35,
                         h=23,
                         label=-1):
        sdf_func = sdf.rounded_cone(ra, rb, h).translate((0, 0, -h / 2))
        r = max(ra, rb)
        xyz_size = [2 * r, h, 2 * r]
        return Object3D_diaoBi(t=t,
                               angles=angles,
                               xyz_size=xyz_size,
                               label=label,
                               sdf_func=sdf_func)

    @classmethod
    def get_steel_pipes_byrow(self,
                              t,
                              angles=[0, np.pi / 4, 0],
                              ra=1,
                              rb=0.2,
                              h=3,
                              label=-1,
                              num_pipe=5):
        """成排钢管

        Args:
            t (_type_): _description_
            angles (list, optional): 考虑到吊钢管时都成排,故一般仅需要随机Z轴. Defaults to [np.pi / 2, 0, np.pi / 4].
            ra (int, optional): 单个钢管半径. Defaults to 1.
            rb (float, optional): 钢管圆角参数. Defaults to 0.2.
            h (int, optional): 钢管长. Defaults to 3.
            label (int, optional): _description_. Defaults to -1.
            num_pipe (int, optional): 钢管个数. Defaults to 5.

        Returns:
            _type_: _description_
        """
        center_pos_x = np.arange(0 - num_pipe * ra + ra,
                                 0 + num_pipe * ra - ra + 0.01, 2 * ra)

        for i in range(num_pipe):
            pos = [center_pos_x[i], 0, 0]
            if i == 0:
                sdf_func = sdf.rounded_cylinder(ra, rb, h).translate(pos)
            else:
                sdf_func |= sdf.rounded_cylinder(ra, rb, h).translate(pos)
        # return obj_list
        xyz_size = [2 * ra * num_pipe, 2 * ra, h]
        return Object3D(t=t,
                        angles=angles,
                        xyz_size=xyz_size,
                        label=label,
                        sdf_func=sdf_func,
                        R=np.eye(3))

    @classmethod
    def get_wireframe_box(self,
                          t,
                          angles=[0, 0, 0],
                          xyz_size=[1, 2, 3],
                          thickness=0.2,
                          num_box=1,
                          label=-1):
        """类脚手架结构

        Args:
            t (_type_): _description_
            angles (list, optional): _description_. Defaults to [0, np.pi / 4, 0].
            xyz_size (list, optional): _description_. Defaults to [1, 2, 3].
            thickness (float, optional): _description_. Defaults to 0.2.
            num_box (int, optional): _description_. Defaults to 1.
            label (int, optional): _description_. Defaults to -1.

        Returns:
            _type_: _description_
        """
        xyz_size_ori = deepcopy(xyz_size)
        xyz_size = list(map(lambda x: x * 0.65, xyz_size))
        sdf_func = sdf.wireframe_box(xyz_size, thickness)
        for _ in range(1, num_box):
            xyz_size = list(map(lambda x: x / 2, xyz_size))
            sdf_func |= sdf.wireframe_box(xyz_size, thickness)
        obj = Object3D(t=t,
                       angles=angles,
                       xyz_size=xyz_size_ori,
                       label=label,
                       sdf_func=sdf_func)
        return obj

    @classmethod
    def get_random_obj(self, t, label, min_size, max_size, obj_names=['rounded_box', 'rounded_cylinder','sphere','steel_pipes_byrow']):
        selected_name = np.random.choice(obj_names, 1)[0]
        if selected_name == 'rounded_box':
            xyz_size = np.random.uniform(min_size, max_size, size=3)
            obj = ObjectFactory.get_rounded_box(t=t,
                                                angles=[0,0,0],
                                                xyz_size=xyz_size,
                                                label=label)
        elif selected_name == 'rounded_cylinder':
            ra = np.random.uniform(0.2, 1)
            rb = 0.1
            h = np.random.uniform(min_size, max_size)
            obj = ObjectFactory.get_rounded_cylinder(t=t,
                                                     ra=ra,
                                                     rb=rb,
                                                     h=h,
                                                     label=label)
        elif selected_name == 'wireframe_box':
            thickness = np.random.uniform(0.1, 0.5)
            num_box = np.random.randint(1, 4)
            xyz_size = np.random.uniform(min_size, max_size, size=3)
            obj = ObjectFactory.get_wireframe_box(t=t,
                                                  xyz_size=xyz_size,
                                                  thickness=thickness,
                                                  num_box=num_box,
                                                  label=label)
        elif selected_name == 'sphere':
            if min_size//2<=max_size//2:
                r = 2
            else:
                r = np.random.randint(min_size//2, max_size//2)
            obj = ObjectFactory.get_sphere(t=t,angles=[0,0,0],r=r,label=label)
        elif selected_name == 'steel_pipes_byrow':
            ra = np.random.uniform(1, 3)
            rb = 0.1
            h = np.random.uniform(min_size, max_size)
            num_pipe = np.random.randint(3, 6)
            angles = [0, np.random.uniform(-np.pi, np.pi), 0]
            obj = ObjectFactory.get_steel_pipes_byrow(t=t,
                                                      angles=angles,
                                                      ra=ra,
                                                      rb=rb,
                                                      h=h,
                                                      label=label,
                                                      num_pipe=num_pipe)
        return obj


def get_link_point(lidar, num_lines, grid_obj, distance_first=False):
    """找出在雷达视角下物体作为链接绳子的可见点
    TODO: 优化链接点返回逻辑,对于靠近对方链接点的点优先返回

    Args:
        lidar (_type_): _description_
        num_lines (_type_): _description_
        grid_obj (_type_): _description_
        distance_first: 距离雷达近的优先返回
    Returns:
        _type_: _description_
    """
    grid_obj = [grid_obj] if isinstance(grid_obj, GridObject) else grid_obj
    scene = Scene(grid_obj)
    # 返回世界坐标系下链接点坐标
    scene_pts = lidar.synthethis(scene,
                                 0,
                                 1,
                                 10000,
                                 0.001,
                                 return_scene_coord=True)
    if len(scene_pts) < num_lines:
        print('can not find enough lifting pts')
        return np.zeros((0, 3))
    obj_center = np.mean(np.concatenate(
        [obj.obj2scene[:, 3].reshape(1, 3) for obj in grid_obj], 0),
                         axis=0)
    if distance_first:
        distance = np.linalg.norm(scene_pts[:, :3] - obj_center, axis=1)
        indices = np.argsort(distance)
        sampled_pts = scene_pts[indices[:num_lines]][:, :3]
    else:
        sampled_pts = util.farthest_point_sample(scene_pts, num_lines)[:, :3]
    return sampled_pts


def insert_person_objs(
        diaobi_obj: GridObject,
        #    diaowu_obj: GridObject,
        scene_center: np.ndarray,
        scan_radius=18,
        person_grid_paths=[]):
    """构建人

    Args:
        diaobi_obj (GridObject): _description_
        diaowu_obj (GridObject): _description_
        r (int, optional): 以吊物为中心r为半径区域内构造人. Defaults to 18.
        person_grid_paths (list, optional): _description_. Defaults to [].

    Returns:
        List: person_obj_list
    """
    num_person = np.random.randint(4, 8)
    diaobi_upper_coord = diaobi_obj.update_diaobi_upper_pt()

    r = np.sqrt(scan_radius**2 - scene_center[1]**2) * 0.8
    candidate_pos = _utils.get_pos_grid(
        [diaobi_upper_coord[0] - r, diaobi_upper_coord[0] + r], [-0.5, 0.1],
        [diaobi_upper_coord[2] - r, diaobi_upper_coord[2] + r])
    distances = np.linalg.norm(candidate_pos - scene_center, axis=1)
    valid = distances < scan_radius
    candidate_pos = candidate_pos[valid]
    np.random.shuffle(candidate_pos)
    # 吊臂与吊物投影下方不会有人
    # invalid_pos = np.concatenate(
    #     [diaowu_obj.sample_neg_grid_pts(),
    #      diaobi_obj.sample_neg_grid_pts()], 0)
    invalid_pos = _utils.random_sample_pts(diaobi_obj.sample_neg_grid_pts(),
                                           600)
    valid_candidate_pos = []
    for pos in candidate_pos:
        min_distance = np.min(np.linalg.norm(invalid_pos - pos))
        person2diaowu_distance = np.linalg.norm(pos[[0, 2]] -
                                                diaobi_upper_coord[[0, 2]])
        if min_distance >= 1 and person2diaowu_distance >= 1:
            valid_candidate_pos.append(pos)
    valid_candidate_pos = np.asarray(valid_candidate_pos)
    # valid_candidate_pos = util.farthest_point_sample(valid_candidate_pos,
    #                                                  num_person)

    person_obj_list = []
    person_pos_list = []
    for pos in valid_candidate_pos:
        if len(person_obj_list) == num_person: break
        if len(person_pos_list) > 0:
            distances = np.linalg.norm(np.array(person_pos_list) - pos, axis=1)
            if min(distances) < 3: continue
        grid_path = person_grid_paths[np.random.randint(
            0, len(person_grid_paths))]
        scale = np.random.uniform(1.7, 2.3)
        angle_y = np.random.uniform(-np.pi, np.pi)

        obj = GridObject(grid_sdf_path=grid_path,
                         t=pos,
                         angles=[0, angle_y, 0],
                         obj_scale=scale,
                         label=cls2label['person'])
        # move people on ground
        obj_pts = obj.sample_neg_grid_pts()
        margin_y = 0 - np.max(obj_pts[:, 1])
        obj.obj2scene[:, 3][1] += margin_y
        pos[1] += margin_y
        person_obj_list.append(obj)
        person_pos_list.append(pos)

    return person_obj_list


def insert_bg_objs(scene_obj_list: List[GridObject],
                   bg_grid_paths: List[str],
                   scene_center: np.array,
                   r: float,
                   num_bg,
                   max_size: float,
                   collision_distance=0.5):
    scene_obj_pts = []
    for obj in scene_obj_list:
        pts = obj.sample_neg_grid_pts()
        pts = pts[np.random.randint(0, len(pts), 300)]
        scene_obj_pts.append(pts)
    scene_obj_pts = np.concatenate(scene_obj_pts, 0)
    num_scene_obj_pt = len(scene_obj_pts)

    candidate_pos = _utils.get_pos_grid(
        [scene_center[0] - r, scene_center[0] + r], [-2, 0],
        [scene_center[2] - r, scene_center[2] + r],
        step=1)
    np.random.shuffle(candidate_pos)
    bg_obj_list = []
    for pos in candidate_pos:
        if len(bg_obj_list) == num_bg: break
        angles = np.random.uniform(-np.pi, np.pi, (3, ))
        if np.random.uniform(0, 1) > 0.6:
            scale = np.random.uniform(1, max_size)
            grid_path = bg_grid_paths[np.random.randint(0, len(bg_grid_paths))]
            obj = GridObject(grid_sdf_path=grid_path,
                             t=pos,
                             angles=angles,
                             obj_scale=scale,
                             label=cls2label['bg'])
        else:
            obj = ObjectFactory.get_random_obj(t=pos,
                                               label=cls2label['bg'],
                                               min_size=2,
                                               max_size=max_size)
        obj_pts = obj.sample_neg_grid_pts()
        obj_pts = _utils.random_sample_pts(obj_pts, 300)
        num_obj_pt = len(obj_pts)
        distances = np.tile(scene_obj_pts[None, ...],
                            (num_obj_pt, 1, 1)) - np.tile(
                                obj_pts[:, None, :], (1, num_scene_obj_pt, 1))
        distances = np.linalg.norm(distances.reshape(-1, 3), axis=1)
        min_distance = np.min(distances)
        num_try = 0
        while min_distance < collision_distance:
            if num_try > 2: break
            obj.scale(0.5)
            obj_pts = obj.sample_neg_grid_pts()
            obj_pts = _utils.random_sample_pts(obj_pts, 300)
            num_obj_pt = len(obj_pts)
            distances = np.tile(scene_obj_pts[None, ...],
                                (num_obj_pt, 1, 1)) - np.tile(
                                    obj_pts[:, None, :],
                                    (1, num_scene_obj_pt, 1))
            distances = np.linalg.norm(distances.reshape(-1, 3), axis=1)
            min_distance = np.min(distances)
            num_try += 1
        if min_distance < collision_distance: continue
        bg_obj_list.append(obj)

    return bg_obj_list


def insert_qianyinsheng_objs(lidar, shengzi_grid_pt_path, diaowu_obj_list, person_obj_list):
    diaowu_center = np.mean(np.concatenate(
        [obj.obj2scene[:, 3].reshape(1, 3) for obj in diaowu_obj_list], 0),
                            axis=0)
    # diaowu_center = diaowu_obj.obj2scene[:, 3]
    shengzi_obj_list = []
    num_shengzi = min(np.random.randint(2, 5), len(person_obj_list))
    link_person_objs = random.sample(person_obj_list, num_shengzi)

    for person_obj in link_person_objs:
        person_pos = person_obj.obj2scene[:, 3]
        lidar.move(person_pos, diaowu_center)
        link_pts = get_link_point(lidar=lidar,
                                  num_lines=1,
                                  grid_obj=diaowu_obj_list,
                                  distance_first=False)
        if len(link_pts) == 0: continue
        link_diaowu_pos = link_pts[0]
        lidar.move(diaowu_center, person_pos)
        link_pts = get_link_point(lidar=lidar,
                                  num_lines=1,
                                  grid_obj=person_obj,
                                  distance_first=False)
        if len(link_pts) == 0: continue
        link_person_pos = link_pts[0]

        rope_scale, rope_R, rope_t = _utils.get_cylinder_params(
            shengzi_grid_pt_path, link_diaowu_pos, link_person_pos)
        diaosheng_obj = ShengZi(grid_sdf_path=shengzi_grid_pt_path,
                                t=rope_t,
                                angles=rope_R,
                                obj_scale=rope_scale,
                                label=cls2label['qianYinSheng'])
        shengzi_obj_list.append(diaosheng_obj)
    return shengzi_obj_list


def insert_diaowu(diaobi_upper_coord, shapenet_grid_paths, diaobi_obj,
                  scene_center, lidar):
    """_summary_
    Args:
        diaobi_upper_coord (_type_): _description_
        shapenet_grid_paths (_type_): _description_
        diaobi_obj (_type_): _description_
        scene_center (_type_): _description_
        lidar (_type_): _description_

    Returns:
        _type_: _description_
    """
    diaowu_pos = deepcopy(diaobi_upper_coord)
    y_list = np.arange(diaobi_upper_coord[1] * 1 / 4, -0.1, step=0.2)
    # 设置吊物高度概率分布
    diaowu_y = _utils.sample_number_from_gaussian(y_list,
                                                  1,
                                                  sigma=1,
                                                  peak_loc=0.2)
    diaowu_pos[1] = diaowu_y
    num_pipes = None
    if np.random.uniform(0, 1) < 0.5:
        diaowu_obj = GridObject(grid_sdf_path=random.sample(
            shapenet_grid_paths, 1)[0],
                                t=diaowu_pos,
                                angles=np.random.uniform(-np.pi, np.pi, (3, )),
                                obj_scale=np.random.uniform(2, 10),
                                label=cls2label['diaoWu'])

    else:
        diaowu_obj = ObjectFactory.get_random_obj(t=diaowu_pos,
                                                    label=cls2label['diaoWu'],
                                                    min_size=3,
                                                    max_size=12,
                                                    obj_names=['rounded_box'])
    diaowu_obj_list = [diaowu_obj]
    # 保证吊物在地面以上且在吊臂以下
    diaowu_pts = np.concatenate(
        [obj.sample_neg_grid_pts() for obj in diaowu_obj_list], 0)
    max_y = max(diaowu_pts[:, 1])
    if max_y > -0.3:
        for obj in diaowu_obj_list:
            obj.obj2scene[:, 3][1] -= max_y
    min_y = max(diaowu_pts[:, 1])
    if min_y - diaobi_upper_coord[1] < 2:
        for obj in diaowu_obj_list:
            obj.obj2scene[:, 3][1] += 3 - (min_y - diaobi_upper_coord[1])

    # 确保吊物不会与吊臂碰撞
    diaowu_pts = np.concatenate(
        [obj.sample_neg_grid_pts() for obj in diaowu_obj_list], 0)
    diaobi_pts = diaobi_obj.sample_neg_grid_pts()
    num_sample = 500 * num_pipes if num_pipes is not None else 500
    diaowu_idxs = np.random.randint(0, len(diaowu_pts), num_sample)
    diaowu_pts = diaowu_pts[diaowu_idxs]
    diaobi_idxs = np.random.randint(0, len(diaobi_pts), 300)
    diaobi_pts = diaobi_pts[diaobi_idxs]
    distances = np.tile(diaowu_pts[None, ...],
                        (len(diaobi_pts), 1, 1)) - np.tile(
                            diaobi_pts[:, None, :], (1, len(diaowu_pts), 1))
    distances = np.linalg.norm(distances.reshape(-1, 3), axis=1)
    min_distance = min(distances)
    if min_distance < 1:
        for obj in diaowu_obj_list:
            obj.scale(min_distance)
            print(obj.size)

    # 计算吊物吊绳挂点
    # debug
    # np.savetxt('results/diaowu_pts.txt', diaowu_pts)
    diaowu_center = diaowu_pts.mean(0)
    p_lidar_new = deepcopy(diaowu_center)
    p_lidar_new[1] -= 10
    lidar.move(p_lidar_new, diaowu_center)
    link_pts = get_link_point(lidar=lidar,
                              num_lines=np.random.randint(2, 5),
                              grid_obj=diaowu_obj_list)
    return diaowu_obj_list, link_pts


def insert_diaoBi(scene_center, diaobi_grid_paths):
    diaobi_pos = deepcopy(scene_center)
    if np.random.uniform(0, 1) > 0.7:
        diaobi_obj = DiaoBi(grid_sdf_path=random.sample(diaobi_grid_paths,
                                                        1)[0],
                            t=diaobi_pos,
                            angles=[
                                0,
                                np.random.uniform(-np.pi / 2, np.pi / 2),
                                np.random.uniform(-np.deg2rad(30),
                                                  np.deg2rad(30))
                            ],
                            obj_scale=np.random.uniform(15, 35),
                            label=cls2label['diaoBi'])
    else:
        length = np.random.uniform(20, 33)
        ra = np.random.uniform(0.5, 1.2)
        rb = ra - np.random.uniform(0.28, 0.45)
        angles = [
            0,
            np.random.uniform(0, 2 * np.pi),
            _utils.sample_number_from_gaussian(
                np.arange(np.pi / 7, np.pi / 4, np.pi / 180), 1)[0],
        ]
        diaobi_obj = ObjectFactory.get_rounded_cone(t=diaobi_pos,
                                                    angles=angles,
                                                    ra=ra,
                                                    rb=rb,
                                                    h=length,
                                                    label=cls2label['diaoBi'])
    diaobi_pts = _utils.random_sample_pts(diaobi_obj.sample_neg_grid_pts(),
                                          500)
    margin_y = 0 - np.max(diaobi_pts[:, 1])
    diaobi_obj.obj2scene[:, 3][1] += margin_y
    return diaobi_obj
