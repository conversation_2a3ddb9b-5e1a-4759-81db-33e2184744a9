import numpy as np
from typing import List
from scripts import util
import torch
import os

np.random.seed()


def get_transform_mat(t, angles):
    if type(angles) is np.ndarray:
        if len(angles.shape) == 2:
            R = angles
        else:
            R = get_rotation_mat('xyz', angles)
    else:
        R = get_rotation_mat('xyz', angles)

    return np.concatenate([R, t.reshape(3, 1)], axis=1)


def get_rotation_mat(axis='x', angles=[0]):
    """旋转矩阵计算 左手坐标系旋转！！！TODO 统一到右手坐标系
    Args:
        axis (str, optional): _description_. Defaults to 'x'.
        angles (list, optional): _description_. Defaults to [0].

    Returns:
        _type_: _description_
    """
    if isinstance(angles, float):
        angles = [angles]

    assert (len(axis) == len(angles))

    R = np.eye(3)
    for ax, angle in zip(axis, angles):
        _R = {
            'x': get_rotation_mat_x,
            'y': get_rotation_mat_y,
            'z': get_rotation_mat_z
        }[ax](angle)

        R = R @ _R

    return R


def get_rotation_mat_x(theta):
    return np.array([[1, 0, 0], [0, np.cos(theta),
                                 np.sin(theta)],
                     [0, -np.sin(theta), np.cos(theta)]])


def get_rotation_mat_y(theta):
    return np.array([[np.cos(theta), 0, -np.sin(theta)], [0, 1, 0],
                     [np.sin(theta), 0, np.cos(theta)]])


def get_rotation_mat_z(theta):
    return np.array([[np.cos(theta), np.sin(theta), 0],
                     [-np.sin(theta), np.cos(theta), 0], [0, 0, 1]])


def get_pos_grid(x_range: List, y_range: List, z_range: List, step=0.5):
    """在指定XYZ范围构建采样点

    Args:
        x_range (List): _description_
        y_range (List): _description_
        z_range (List): _description_

    Returns:
        sample pts: ndarray
    """
    sx = np.arange(x_range[0], x_range[1], step)
    sy = np.arange(y_range[0], y_range[1], step)
    sz = np.arange(z_range[0], z_range[1], step)
    xx, yy, zz = np.meshgrid(sx, sy, sz, indexing='ij')
    grid_points_mnp = np.stack([xx, yy, zz], -1)  # MNP
    sample_pts = grid_points_mnp.reshape(-1, 3)
    return sample_pts


def get_cylinder_params(pt_file, p1, p2):
    """
    根据空间中两点p1 p2生成牵引绳素材的索引,p1 应高于 p2, 即在天理坐标系中 p1_y < p2_y

    """
    data = torch.load(pt_file)
    obj_xyz_size = np.array(data['max_bound'] - data['min_bound'],
                            dtype=np.float32)
    s = (p1 - p2) / np.linalg.norm(p1 - p2)
    t = (p1 + p2) / 2
    dis = np.linalg.norm(p1 - p2)
    cylinder_n = np.array([0, -1, 0])

    scale = dis / obj_xyz_size[1]
    q = util.QuaternionHandler()
    Rq1 = q.getQuaternion(cylinder_n, s)
    if len(Rq1) == 1:
        if Rq1[0] == 0:
            r = np.eye(3)
        else:
            r = np.array([[-1, 0, 0], [0, 1, 0], [0, 0, -1]])
    else:
        r = q.getRotation(Rq1)
    return scale, r, t

def create_look_at_matrix(position, target, world_up=[0,1,0]):
    """
    根据位置、目标和全局上方向，为 RDF (Right, Down, Forward) 坐标系
    构建一个姿态矩阵（旋转矩阵）。

    Args:
        position (np.ndarray): 物体/雷达的位置。
        target (np.ndarray): 观察目标的位置。
        world_up (np.ndarray): 全局的上方向向量 (e.g., [0, 1, 0] for Y-up world).

    Returns:
        np.ndarray: 3x3 的旋转矩阵，代表了物体的 RDF 姿态。
    """
    position = np.asarray(position, dtype=float)
    target = np.asarray(target, dtype=float)
    world_up = np.asarray(world_up, dtype=float)

    # 1. 计算 Forward 轴 (Z-axis)
    z_axis_forward = target - position
    # 处理位置和目标点重合的边缘情况
    if np.linalg.norm(z_axis_forward) < 1e-6:
        return np.identity(3) # 返回单位矩阵，表示无特定朝向
    z_axis_forward = z_axis_forward / np.linalg.norm(z_axis_forward)

    # 2. 计算 Right 轴 (X-axis)
    x_axis_right = np.cross(world_up, z_axis_forward)
    # 处理当 'forward' 和 'world_up' 平行时的边缘情况 (万向节死锁)
    if np.linalg.norm(x_axis_right) < 1e-6:
        # 当雷达直视世界坐标系的正上方或正下方时，
        # world_up 不能用于定义 "Right"。
        # 我们使用一个备用参考，例如世界坐标系的X轴 [1, 0, 0]。
        # 注意：这里的备用参考选择会影响看向正上方时的“航向”
        alternative_ref = np.array([1, 0, 0])
        x_axis_right = np.cross(alternative_ref, z_axis_forward)
        # 如果恰好也与备用参考平行(看向世界X轴)，则换一个
        if np.linalg.norm(x_axis_right) < 1e-6:
            alternative_ref = np.array([0, 0, -1]) # 使用世界Z轴
            x_axis_right = np.cross(alternative_ref, z_axis_forward)
            
    x_axis_right = x_axis_right / np.linalg.norm(x_axis_right)

    # 3. 计算 Down 轴 (Y-axis)
    # 通过叉乘确保所有轴严格正交 (Forward x Right = Down)
    y_axis_down = np.cross(z_axis_forward, x_axis_right)

    # 4. 组合成姿态矩阵
    # 姿态矩阵的列是物体局部坐标系的基向量在世界坐标系中的表示
    # R = [X_axis | Y_axis | Z_axis]
    rotation_matrix = np.stack([
        x_axis_right,
        y_axis_down,
        z_axis_forward
    ], axis=1)

    return rotation_matrix


def calculate_stable_rotation(pos1, target1, pos2, target2, world_up=np.array([0, 1, 0])):
    """
    计算雷达从 pos1 移动到 pos2的旋转矩阵

    Args:
        pos1 (np.ndarray): 雷达的初始位置。
        target1 (np.ndarray): 雷达的初始目标。
        pos2 (np.ndarray): 雷达的最终位置。
        target2 (np.ndarray): 雷达的最终目标。
        scene_center (np.ndarray): 观测中心。
        world_up (np.ndarray, optional): 全局上方向。默认为 [0, 1, 0]。

    Returns:
        np.ndarray: 3x3 的旋转矩阵，描述了从姿态1到姿态2的变换。
    """
    # 1. 计算初始姿态矩阵
    R1 = create_look_at_matrix(pos1, target1, world_up)

    # 2. 计算最终姿态矩阵
    R2 = create_look_at_matrix(pos2, target2, world_up)

    # 3. 计算从 R1 到 R2 的差分旋转
    # 在Lidar中定义R_new = R@R_diff
    R_diff = R1.T@R2

    return R_diff

def init_lidar_pose(lidar_ori_pos,scene_center,world_up=np.array([0, 1, 0])):
    """初始化雷达的姿态, 将雷达指向scene_center, 移动至lidar_ori_pos

    Args:
        lidar_ori_pos (_type_): 雷达初始位置（需要挪至的位置）
        scene_center (_type_): 场景中心
        world_up (_type_, optional): 计算look-at矩阵时使用的world_up方向向量, 一般不需要修改. Defaults to np.array([0, 1, 0]).

    Returns:
        R: R1->R2 matrix
    """
    R1 = create_look_at_matrix(lidar_ori_pos, [0,0,1],world_up)
    R2 = create_look_at_matrix(lidar_ori_pos, scene_center,world_up)
    R_diff = R1.T@R2
    return R_diff


def get_lidar_pos_params(p_lidar_ori,target_ori,p_lidar_new,target_new):
    """
    根据场景中心 雷达原位置 以及 雷达移动后位置 确定雷达的位姿变换矩阵

    Args:
        p_obj: 原坐标系下的场景中心 [0, 0, z_o]
        p_lidar_ori: 默认在原点
        p_lidar_new: 雷达要移动到的位置

    Returns: R, t

    """

    '''
    # assert np.sum(p_lidar_ori) == 0, "p_lidar_ori is not at the origin !"
    assert p_lidar_ori[0]==0 and p_lidar_ori[2]==0
    assert not np.array_equal(p_obj, p_lidar_new)

    # q = util.QuaternionHandler()

    # 先绕Y轴旋转 lidar coor1
    p_lidar_coor1 = np.array([p_lidar_new[0], 0., p_lidar_new[2]])
    p_obj_proj = np.array([p_obj[0], 0., p_obj[2]])
    p_obj_coor1 = p_obj_proj - p_lidar_coor1
    from_v1 = np.array([0, 0, 1])
    to_v1 = np.array(p_obj_coor1)
    Ry = util.get_rotation_matrix_from_vectors(from_v1, to_v1)
    # if (to_v1 == 0).all():
    #     Ry = np.eye(3)
    # else:
    #     Rq1 = q.getQuaternion(from_v1, to_v1)
    #     if len(Rq1) == 1:
    #         if Rq1[0] == 0:
    #             Ry = np.eye(3)
    #         else:
    #             Ry = np.array([[-1, 0, 0], [0, 1, 0], [0, 0, -1]])
    #     else:
    #         Ry = q.getRotation(Rq1)

    # 再绕X轴旋转 lidar coor2
    # p点在原雷达坐标系下的表示
    p_obj_coor2 = p_obj - p_lidar_new
    # 点不动，转雷达坐标系，计算点在新坐标系下表示
    p_obj_coor2_Ry = ((Ry.T @ p_obj_coor2[..., None]).T).squeeze()
    # p_obj_coor2_Ry = Ry.dot(p_obj_coor2)
    # print(p_obj_coor2_Ry)
    # from_v2 = np.array([0, 0, 1])
    from_v2 = np.array([0, p_obj[1]-p_lidar_ori[1], 1])
    to_v2 = np.array(p_obj_coor2_Ry)
    Rx = util.get_rotation_matrix_from_vectors(from_v2, to_v2)
    # Rq2 = q.getQuaternion(from_v2, to_v2)
    # if len(Rq2) == 1:
    #     if Rq2[0] == 0:
    #         Rx = np.eye(3)
    #     else:
    #         Rx = np.array([[1, 0, 0], [0, -1, 0], [0, 0, -1]])
    # else:
    #     Rx = q.getRotation(Rq2)

    R = Ry @ Rx
    # R = Rx @ Ry
    # R = Rx.T @ Ry.T
    t = p_lidar_new
    '''
    
    R = calculate_stable_rotation(p_lidar_ori,target_ori, p_lidar_new, target_new)
    t = p_lidar_new
    return R, t


def random_sample_pts(pts, num_sample):
    if len(pts) > num_sample:
        replace = False
    else:
        replace = True
    indices = np.random.choice(len(pts), size=num_sample, replace=replace)
    return pts[indices]


def sample_number_from_gaussian(sample_list,
                                num_samples,
                                peak_loc=0.5,
                                sigma=1):
    """对样本以高斯分布概率来采样

    Args:
        sample_list (_type_): _description_
        num_samples (_type_): _description_
        peak_loc (float, optional): 峰值点在列表位置,0-1. Defaults to 0.5.

    Returns:
        _type_: _description_
    """
    # mu = np.mean(sample_list)
    assert 0 < peak_loc < 1
    N = len(sample_list)
    mu = sample_list[int(N * peak_loc)]
    pdf = np.exp(-((sample_list - mu)**2) /
                 (2 * sigma**2)) / (sigma * np.sqrt(2 * np.pi))

    # 归一化PDF，确保概率之和为1
    pdf = pdf / pdf.sum()
    samples = np.random.choice(sample_list, num_samples, p=pdf)
    return samples


def mkdir_or_exist(dir_name, mode=0o777):
    if dir_name == '':
        return
    dir_name = os.path.expanduser(dir_name)
    os.makedirs(dir_name, mode=mode, exist_ok=True)


def sample_lidar_pos(scene_center, lidar_ori=np.array([0, 0, 0]), num_lidar=8):
    lidar_pos_list = []
    step = 2 * np.pi / num_lidar
    for i in range(num_lidar):
        new_lidar_pos = util.rotate_pts_np(ptA=scene_center,
                                           ptsB=lidar_ori,
                                           angles_XYZ=[0, -step * i, 0])
        lidar_pos_list.append(new_lidar_pos)
    return lidar_pos_list