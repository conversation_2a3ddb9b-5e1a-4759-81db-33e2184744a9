import numpy as np

from .scene import Scene
from prism import DoublePrism

from . import _utils
from scripts import util
from scene import cls2label


class Lidar:

    def __init__(
        self,
        # 雷达内参
        ng,
        alpha,
        omega1,
        omega2,
        # 雷达外参
        t=np.zeros(3),
        angles=np.zeros(3)):
        self.prism = DoublePrism(ng, alpha, omega1, omega2)

        self.lidar2scene = _utils.get_transform_mat(t, angles)

    def moveto(self, t):
        self.lidar2scene[:, 3] = t

    def rotate(self, axis='xyz', angles=[0, 0, 0], R_matrix=None):
        R = self.lidar2scene[:, :3]

        if R_matrix is None:
            _R = _utils.get_rotation_mat(axis, angles)
        else:
            _R = R_matrix

        self.lidar2scene[:, :3] = R @ _R
    
    
    def get_lidar_direction(self):
        """雷达朝向""" 
        return self.lidar2scene[:, :3] @ np.array([0, 0, 1])
    
    def get_lidar_position(self):
        """雷达位置"""
        return self.lidar2scene[:, 3]
    
    def move(self, target_pos, target_direction):
        """将雷达移动到目标位置，并且旋转到目标朝向

        Args:
            target_pos (_type_): 目标位置
            target_direction (_type_): 目标朝向
        """
        R, t = _utils.get_lidar_pos_params([0,0,0],self.get_lidar_direction(),target_pos,target_direction)
        self.moveto(t)
        self.rotate(R_matrix=R)

    def synthethis(self,
                   scene: Scene,
                   start_time: float,
                   T: float,
                   N: int,
                   step: float,
                   return_scene_coord=False):
        outer_rays_d: np.ndarray = self.prism.integrate(start_time, T, N)
        outer_rays_o: np.ndarray = np.zeros_like(outer_rays_d)

        # 将雷达坐标系中的光线转换到场景的世界坐标系
        R: np.ndarray = self.lidar2scene[:, :3]
        t: np.ndarray = self.lidar2scene[:, 3]

        outer_rays_d = outer_rays_d.dot(R.T)
        outer_rays_o = outer_rays_o.dot(R.T) + t

        # debug
        '''
        # save the ray with sampled points
        sample = np.arange(0,20,0.5)
        rays = outer_rays_o[:, np.newaxis, :] + sample[np.newaxis, :, np.newaxis] * outer_rays_d[:, np.newaxis, :]
        sample_pts = outer_rays_o + 10*outer_rays_d
        
        # 随机采样1000条ray
        rays = rays[np.random.randint(0, rays.shape[0], 1000),...]
        np.savetxt('results/sample_pts.txt', sample_pts)
        np.savetxt('results/ray.txt', rays.reshape(-1,3))
        '''
        # 点云合成，返回的点依然在世界坐标系中
        points = scene.synthethis(outer_rays_o, outer_rays_d, step)

        if len(points) == 0: return points

        if not return_scene_coord:
            # 转回雷达坐标系
            points[:, :3] = points[:, :3].dot(R) - R.T.dot(t)

        return points

    def convert2scene(self, points):
        """将雷达坐标系下的点转到世界坐标系
        """
        # 将雷达坐标系中的光线转换到场景的世界坐标系
        R: np.ndarray = self.lidar2scene[:, :3]
        t: np.ndarray = self.lidar2scene[:, 3]
        points_scene = np.copy(points)
        points_scene[:, :3] = points_scene[:, :3].dot(R.T) + t
        return points_scene

    def get_diaobi_upper_pt_lidar(self, scene):
        """
        获取雷达坐标系下的吊臂顶点坐标

        """
        self.diaobi_upper_pt_lidar = None
        R: np.ndarray = self.lidar2scene[:, :3]
        t: np.ndarray = self.lidar2scene[:, 3]
        diaobi_upper_pt_scene = scene.update_diaobi_upper_scene()
        if len(diaobi_upper_pt_scene) > 0:
            self.diaobi_upper_pt_lidar = diaobi_upper_pt_scene.dot(
                R) - R.T.dot(t)

        return self.diaobi_upper_pt_lidar

    def get_lidar_R(self,
                    ori_d=np.array([0, 0, 1]),
                    target_d=np.array([0, 0, 1])):
        """获取雷达旋转矩阵

        Args:
            ori_d (_type_, optional): 雷达原始朝向. Defaults to np.array([0, 0, 1]).
            target_d (_type_, optional): 雷达目标朝向. Defaults to np.array([0, 0, 1]).

        Returns:
            _type_: 雷达旋转矩阵
        """

        R = util.quaternion_from_two_vectors(ori_d, target_d)
        return R
